#toast-container>div,.modal,.modal-open .modal,.sr-only,.text-overflow,body.overflowed {
    overflow: hidden
}

.account div>span,.catalog-header .catalog-info .catalog-name,.text-overflow {
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis
}

.old-browser .container,.suggestion {
    max-width: 700px
}

*,.modal {
    outline: 0
}

.icon,body {
    -moz-osx-font-smoothing: grayscale
}

.comparator ul ul,.comparator ul ul>li,table {
    border-collapse: collapse
}

.product-tags .tag,.toast-message {
    word-wrap: break-word
}

.editComment h3,.sr-only,.text-line,.text-overflow {
    white-space: nowrap
}

.central-icons,.compreJunto .produto,.flex-column {
    -webkit-box-direction: normal
}

@font-face {
    font-family: go;
    src: url(fonts/go/go-v3.eot);
    src: url(fonts/go/go-v3.eot) format("embedded-opentype"),url(fonts/go/go-v3.woff2) format("woff2"),url(fonts/go/go-v3.woff) format("woff"),url(fonts/go/go-v3.ttf) format("truetype"),url(fonts/go/go-v3.svg) format("svg");
    font-display: swap;
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: icons-new;
    src: url("data:font/woff2;base64,d09GMgABAAAAAAPoAAsAAAAACEAAAAOaAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACDDAqEbIQkATYCJAMQCwoABCAFg3IHShspBxGVmxXJfh7YtpJtsjb6DfXMMpZoDY3wkfSe8x916fuSdSCHgXwuEhhCU4mHiXDkuZ2Bt6QNAOJ/bvf2N9mapdi0NEswJEFwdeouHOAGpBdWvOOX5zj5dZR4b+Jy2iT8BWCNOXHMDZYVWBfQmNtLKJHEPA3zLhPbDvCT14AAUp4a7G7pGoLPP3fDKkRQsEqCwp/+pYvnwqWLKQOBxIU4ps76RGcgAPfxBshIvn4GPrqBEQV2QN/bvAAPf+ZnURaAyLQemYTuDCAAHJB1MSk9w+oBPLNk4WSXWpEitVcQ/Kz///1MRL72Xx5AGA60gfl6AYAiWDn8TMBCwmlgAZwCBrS7McUFcAM+Az8xftQ2L2SU3Z2Rm5StePvo8pgB7DFV9bhNtNfyHLu25/rxm3tvaMeCjKBaGdlbPXY82HQsEaMhV3QwjVFMC0wbYtP0yRrzRhfzmsO8DtbNzZvqMtPeG2JZx4ygKgoJ2hNMZnC1OZIZ2sbMWJN4nFGXpNYUum0xqfRcgKQ1hTE8PHL5qj0WohoJQW05NnpF6kOStD2GMU1k28eKtGMZyia2tDZk8+sqFs8eswRWFRCSkPjYOf6L4xbHw/XKdP/3z4569xWX98fMCXsXP7MsbFZ59LLoes9ENG6uE5oId2x26LLQEslIXl8QxMOFRy4S4b5DvjqUlq8GAs4LFwubCw6hcBWivtdDQ4MIFM0vX+S3nUaHxCL3IoGPi3ZHDytDXNUpMDCfX1J9FY+y3ZvdmvAZv/BQXCgb/Ffc3t7RErO2auOibK+wDa760938gmhZt2aRM3WBs78waJufMr80TNu7vsQZVFjPsQbbiS7KAgAqkgBAi0J/iW74Mq/i7Wzfqr9THa7Zcdzbk1CXWV0YTKZu09gJ/P+f7g+g3jmSRAuoPw9nsLM/GECSgP9gWj/Pt9ASMdkXYJxEAXCCF1rB+wOCKgRwEOIBKVPKbFWgfhx1N4i0gADGxyqAk3agFfwhQAhxBnCQrgCSx73JqpQ4+E/R1m5o30lUIoedXlibpw2mfqx+4oobVsGoQf6hfezMbMBUyrDSxpgSQakUUHHvmMsyDKbrp72ivPOKtnZD+06iUlKmfKFLapBQH6Z+HEJf2dswUjDqVeUf2sfOzAYMKSmDvS4jTWQmziSHXbo7BVTcuwWmsgxyOTOV3R/MzfeI9ktlYMP7vqXSM8zkt4g6NJwTWXXspTkqxW+2U6LybgcAAA==") format("woff2")
}

@font-face {
    font-family: icons-google;
    font-display: swap;
    src: url("data:font/woff2;base64,d09GMgABAAAAAAXYAAsAAAAAC4gAAAWJAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACCfgqLWIknATYCJAMMCwgABCAFg3IHORuJCVGULU6F7Gdh7OYYoj7xN9729rdeVCL6KcJ4fcUSPE921ftVXc1F98o9C52ISHQ0l51jSNk5AmPHszCn2VgRqgwlSnRER11KgdIBGXd3Yy/k+d790EvLdy0nKSL5suRtMl/tNd8GxgoILflZbJuvdnj/c7+X/2+M1EgAe1H3R+Ui2z4OcDbAqqbZdGfgE/M9ZHMY2WwqvvIiCODTghH+Pv1WzxCbP+XkQokgwWSBuqXMn6OJENW9WDNiwXXY2E1liRzGgKR4BTTkP36YDIYCCqMJjjFi5liBz9/yW0kTgGLpT9kHFlaAATTQ5MT8Mm4+OoJACU1yqz8WDVIlt7cQ+K1SqZQt+Gn/wwNBgdgE1jTg1VUp+C0xcdMIYEPc9tnABkoBtbgA7HUyj9fJAVWSlhp+keL0qOy4prhITEhFSo5LTUtsisLiksyN+0GNf1N251sWXkF3kxsl7C0KH+QgI7j0E/VrSaHP8IF0TpupiKVWjt95J7wl7qlKc1PflhvTDrLmYgTqoS53VySRoFwZxi7hCjnvA6cJQYEdVq8xgrUz3A++kL8lboHiaG9lWJ9Bl+7iRuDk0tCm5kFYWUEJhhP0KbTJIBCCwgy5nZQOpXYBBYSTewQMHHZJ5FhAeeUkqxgBQmwfyFGIMCJcWhkMkwbcIObASsAUa2aBqR4sQ5FKA4FSQv50qey6CegFuLk+J1fgA09ocQjMiAuUi9mZmE9ZOdGYmqPzlIyRwHcZELDThQbFkk0dyCrbtkQgIbBxgH3uSju5wZy8XX4/+0YBcz6j4lcF2bUyawGu516qvFlqOMN1+g5ciD4bZmzI6FV9Fb5YLUu33y+9/1lX+DVZ173Jfzp5PtWR0qdBhivqy9vJm88pS8/7+D/m5chZydnoy8Sbo/mKIzfd7nwIO8a794v2k1HHMMlWu7D7kutcan2sNNl6KfFSm3lVy6WYZ52j3RcKL+yOO1bA3e2UDtwmFmYmpgvWF//uGCw/4pXXFQT2qda7U/LeFQUTVk9So0PjQ4+JlydT3SF7ixX79KzcKjgzK2mzd9MpKjmXfYPyUttM9jOPL35CYuR/kTfQDGp1MSNV1CwFIlF4NCVb67C11yrKEkckVApBiFRP3cZkh628VqSVo2K1Lze92jnnnhvkD+1QlGsyvMAEGSNMmryCl2ay47oMqyVpq42OZjdZ0tLKnOfZKiKwXXico7RbUCF+53O6E6ebRJht+uoN7voyXAxYwtREfGXYkvvJql5CWlgtotV6ZJ96bHWfUZFRfbKxuX1GBs85h52Z46KbnQmzI7MvHl0O2ZZIObhERS5RINE9lTvhk6ykXBWpxtiO7zmx8zM/z9o/rWjb5tEdOa60MoQ3YUbrwGQZa5dtjcyC9yx+Lg8sW3627udVjqway2H6d7K70pHZ2E//etarbOryQ1SLZIS8lKDex8iKBg84w4XkVmi4/jFEABbVpeIASIYU/Zvm4+7oWb9mdvnvBv+TKayvfqpyFF9dBOB+1WICGJ+umRnoz0N+9zAJx1jEKMh3ir+YPfHxgRRBPORfPC4YdHNQOJgadGlPA5bFixCIB+QB2Lo9Br1PI43NA7J5IIBYHoLPdKZXZLBQr/FZz7J4O/WGPN70Nj5n9T7j+Fg/oEbsOdPYPT6SdrbFA8P90CsdikbsYlOzFtZnTMmlg3giBTfX4PFKrXuyXMXdpezg+CL3RLbNeLCUTZJPzf3lJ6/X0Hzm2J82txY5YCDDuBx5hNA4YyNyYH3K8CvkYj0Mpkc6NGnPsaZWd95c5AShhCXuafsjFzS6nMKwCsadDzAOyEQMQ4ItAztWb7zchJDlaBNdoNyjRyucSMVam8R25xWi/o+DBHvWokSLhT1rEWs2hZfNNshWAAAAAAA=") format("woff2")
}

.icon {
    display: inline-block;
    font: 1em/1 go;
    speak: none;
    text-transform: none;
    -webkit-font-smoothing: antialiased
}

[class*=" icon-"].v-align-middle,[class^=icon-].v-align-middle,td,th {
    vertical-align: middle
}

.icon-linkedin:before {
    content: "\f101";
    font-family: icons-new
}

.icon-login-new:before {
    content: "\f102";
    font-family: icons-new;
    display: block
}

.icon-shopping-cart:before {
    content: "\f103";
    font-family: icons-new;
    display: block
}

.icon-google:before {
    content: "\f101";
    font-family: icons-google
}

.icon-shield:before {
    content: "\f102";
    font-family: icons-google
}

.icon-arrow-down:before {
    content: "\ea01"
}

.icon-arrow-left:before {
    content: "\ea02"
}

.icon-arrow-right:before {
    content: "\ea03"
}

.icon-card:before {
    content: "\ea04"
}

.icon-cart:before {
    content: "\ea05"
}

.icon-discount:before {
    content: "\ea06"
}

.icon-email:before {
    content: "\ea07"
}

.fotosCompreJunto .plus.to:before,.icon-equal:before {
    content: "\ea08"
}

.icon-facebook:before {
    content: "\ea09"
}

.icon-favorite:before {
    content: "\ea0a"
}

.icon-filter:before {
    content: "\ea0b"
}

.icon-instagram:before {
    content: "\ea0c"
}

.icon-login:before {
    content: "\ea0d"
}

.icon-phone:before {
    content: "\ea0e"
}

.icon-pinterest:before {
    content: "\ea0f"
}

.icon-plus:before {
    content: "\ea10"
}

.icon-reviews:before {
    content: "\ea11"
}

.icon-reward:before {
    content: "\ea12"
}

.icon-search:before {
    content: "\ea13"
}

.icon-security:before {
    content: "\ea14"
}

.icon-sort:before {
    content: "\ea15"
}

.icon-tiktok:before {
    content: "\ea16"
}

.icon-times:before {
    content: "\ea17"
}

.icon-truck:before {
    content: "\ea18"
}

.icon-twitter:before {
    content: "\ea19"
}

.icon-whatsapp:before {
    content: "\ea1a"
}

.icon-youtube:before {
    content: "\ea1b"
}

.product .product-price .product-installments span.preco-avista.precoAvista,.product-wrapper .product-form .product-price-tray #produto_preco #info_preco span.preco-avista.precoAvista,.product-wrapper .product-form .product-reward strong:first-child,.toast-title {
    font-weight: 700
}

.toast-message a,.toast-message label {
    color: #fff
}

.toast-message a:hover {
    color: #ccc;
    text-decoration: none
}

#coments a,.board_htm a,.cart-preview-item-delete,.decoration,.page-noticia .board a,.rte a,.site-lgpd p a {
    text-decoration: underline
}

.toast-close-button {
    position: relative;
    right: -.3em;
    top: -.3em;
    float: right;
    font-size: 1.42857rem;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 1px 0 #fff;
    opacity: .8;
    line-height: 1
}

.toast-close-button:focus,.toast-close-button:hover {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    opacity: .4
}

.rtl .toast-close-button {
    left: -.3em;
    float: left;
    right: .3em
}

button.toast-close-button {
    padding: 0;
    cursor: pointer;
    background: 0;
    border: 0;
    -webkit-appearance: none
}

.toast-top-center,.toast-top-full-width {
    top: 0;
    right: 0;
    width: 100%
}

.toast-bottom-center,.toast-bottom-full-width {
    bottom: 0;
    right: 0;
    width: 100%
}

.toast-top-left {
    top: .85714rem;
    left: .85714rem
}

.toast-top-right {
    top: .85714rem;
    right: .85714rem
}

.toast-bottom-right {
    right: .85714rem;
    bottom: .85714rem
}

.toast-bottom-left {
    bottom: .85714rem;
    left: .85714rem
}

#toast-container {
    position: fixed;
    z-index: 99999999;
    pointer-events: none
}

#toast-container * {
    box-sizing: border-box
}

#toast-container>div {
    position: relative;
    pointer-events: auto;
    margin: 0 0 6px;
    padding: 1.07143rem 1.07143rem 1.07143rem 3.57143rem;
    width: 21.42857rem;
    border-radius: .21429rem;
    background-position: 1.07143rem center;
    background-repeat: no-repeat;
    box-shadow: 0 0 .85714rem #999;
    color: #fff;
    opacity: .8
}

#toast-container>div.rtl {
    direction: rtl;
    padding: 1.07143rem 3.57143rem 1.07143rem 1.07143rem;
    background-position: right 1.07143rem center
}

#toast-container>div:hover {
    box-shadow: 0 0 .14286rem #000;
    opacity: 1;
    cursor: pointer
}

#toast-container>.toast-info {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=)!important
}

#toast-container>.toast-error {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=)!important
}

#toast-container>.toast-success {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==)!important
}

#toast-container>.toast-warning {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGYSURBVEhL5ZSvTsNQFMbXZGICMYGYmJhAQIJAICYQPAACiSDB8AiICQQJT4CqQEwgJvYASAQCiZiYmJhAIBATCARJy+9rTsldd8sKu1M0+dLb057v6/lbq/2rK0mS/TRNj9cWNAKPYIJII7gIxCcQ51cvqID+GIEX8ASG4B1bK5gIZFeQfoJdEXOfgX4QAQg7kH2A65yQ87lyxb27sggkAzAuFhbbg1K2kgCkB1bVwyIR9m2L7PRPIhDUIXgGtyKw575yz3lTNs6X4JXnjV+LKM/m3MydnTbtOKIjtz6VhCBq4vSm3ncdrD2lk0VgUXSVKjVDJXJzijW1RQdsU7F77He8u68koNZTz8Oz5yGa6J3H3lZ0xYgXBK2QymlWWA+RWnYhskLBv2vmE+hBMCtbA7KX5drWyRT/2JsqZ2IvfB9Y4bWDNMFbJRFmC9E74SoS0CqulwjkC0+5bpcV1CZ8NMej4pjy0U+doDQsGyo1hzVJttIjhQ7GnBtRFN1UarUlH8F3xict+HY07rEzoUGPlWcjRFRr4/gChZgc3ZL2d8oAAAAASUVORK5CYII=)!important
}

#toast-container.toast-bottom-center>div,#toast-container.toast-top-center>div {
    width: 21.42857rem;
    margin-left: auto;
    margin-right: auto
}

#toast-container.toast-bottom-full-width>div,#toast-container.toast-top-full-width>div {
    width: 96%;
    margin-left: auto;
    margin-right: auto
}

.toast {
    background-color: #030303
}

.toast-success {
    background-color: #51a351
}

.toast-error {
    background-color: #bd362f
}

.toast-info {
    background-color: #2f96b4
}

.toast-warning {
    background-color: #f89406
}

.toast-progress {
    position: absolute;
    left: 0;
    bottom: 0;
    height: .28571rem;
    background-color: #000;
    opacity: .4
}

#container-add-lista #bloco-add-lista,.banner-home,.brands-custom,.comparator .comparsionFoto,.nav .list,.nav .list>.first-level,.photo-rounded,.photo-square,.product .image,.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label,.products-history .products-history-wrapper,.section-buy-together,.section-notices h2.title-section,.square,.swiper-carousel,.video {
    position: relative
}

@media all and (max-width: 240px) {
    #toast-container>div {
        padding:.57143rem .57143rem .57143rem 3.57143rem;
        width: 11em
    }

    #toast-container>div.rtl {
        padding: .57143rem 3.57143rem .57143rem .57143rem
    }

    #toast-container .toast-close-button {
        right: -.2em;
        top: -.2em
    }

    #toast-container .rtl .toast-close-button {
        left: -.2em;
        right: .2em
    }
}

@media all and (min-width: 241px) and (max-width:480px) {
    #toast-container>div {
        padding:.57143rem .57143rem .57143rem 3.57143rem;
        width: 18em
    }

    #toast-container>div.rtl {
        padding: .57143rem 3.57143rem .57143rem .57143rem
    }

    #toast-container .toast-close-button {
        right: -.2em;
        top: -.2em
    }

    #toast-container .rtl .toast-close-button {
        left: -.2em;
        right: .2em
    }
}

@media all and (min-width: 481px) and (max-width:768px) {
    #toast-container>div {
        padding:1.07143rem 1.07143rem 1.07143rem 3.57143rem;
        width: 25em
    }

    #toast-container>div.rtl {
        padding: 1.07143rem 3.57143rem 1.07143rem 1.07143rem
    }
}

@media(-ms-high-contrast:active),(-ms-high-contrast:none) {
    body {
        opacity: 0;
        visibility: hidden
    }

    body.old-browser {
        opacity: 1;
        visibility: visible
    }

    .old-browser-lost {
        height: 400px
    }
}

.old-browser {
    background-color: #fff;
    min-height: 100vh;
    height: 100%
}

.old-browser .old-browser-title {
    font-size: 1.875rem;
    font-weight: 600;
    text-align: center;
    margin: -1.875rem 0 1.875rem;
    color: #263238
}

.old-browser .old-browser-lost {
    max-width: 400px;
    display: block;
    margin: 0 auto
}

.old-browser .old-browser-info {
    font-size: 1rem;
    font-weight: 300;
    line-height: 1.6
}

.old-browser .old-browser-info p {
    margin-bottom: 1.25rem;
    color: #263238
}

.old-browser .old-browser-options:after {
    content: "";
    clear: both;
    display: table
}

.old-browser .old-browser-options {
    margin: 30px auto;
    width: 520px
}

.old-browser .old-browser-options li {
    float: left;
    width: 80px;
    text-align: center
}

.old-browser .old-browser-options li+li {
    margin-left: 1.875rem
}

.old-browser .old-browser-options li a {
    display: block;
    text-decoration: none;
    color: #263238;
    transition: .2s ease-out
}

.old-browser .old-browser-options li a:hover {
    color: #2db5ff
}

.old-browser .old-browser-options li img {
    width: 60px;
    height: 60px;
    margin: 0 auto
}

.old-browser .old-browser-options li .label {
    font-size: .875rem;
    font-weight: 400;
    display: block;
    text-align: center;
    line-height: 1.4;
    margin-top: .625rem
}

.slcted {
    background-color: #ccc
}

.cl {
    clear: both;
    margin: 0!important
}

.idp {
    font-size: 10px
}

.is-hidden {
    display: none
}

.suggestion {
    z-index: 100;
    min-width: 400px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.photo-rounded,.photo-square,.rounded {
    border-radius: 50%;
    overflow: hidden
}

.suggestion span {
    display: block;
    cursor: pointer;
    padding: 4px 0
}

.page-navegacao_visitados .breadcrumb.central-breadcrumb span.breadcrumb-text,.suggestion-words {
    font-size: 13px
}

.suggestion-words span {
    border-bottom: 1px dotted #ddd
}

.suggestion-products a {
    color: #000;
    font-weight: 700
}

.suggestion-title {
    color: #fff;
    display: block;
    text-indent: 8px;
    font: 700 14px/24px Open Sans Condensed,sans-serif
}

.modal .close span,.perguntasProdutoBTimg,body {
    font-family: var(--font_family)
}

.board_htm table,.cart-preview-table,.catalog-cols .col-content,.comparator ul,.container,.page-contact .board iframe,.page-register fieldset input[size="40"],.page-register fieldset input[size="50"],.product .product-tags .tag:only-child,.product .product-tags[data-tags-count="3"] .tag:first-child,.product .space-image img:not(.loaded),.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp .blocoAlerta,.product-wrapper .product-form .product-price-tray #produto_nao_disp #nao_disp .blocoAlerta,.product-wrapper .product-tabs .tabs-nav .tab-link.active .text:after,.product-wrapper .product-tabs .tabs-nav .tab-link:not(.active):hover .text:after,.row .col.s12,.rte table,.varCont textarea.textarea,img.transform.vertical {
    width: 100%
}

.suggestion-product {
    margin-bottom: 20px;
    font-size: 13px
}

.suggestion-words span:hover {
    background: #eee
}

.suggestion-products strong,.suggestion-words strong {
    background: #666
}

.suggestion-product:hover {
    background: #d8d8d8
}

.modal .close,.modal .modal-content,body {
    background-color: #fff;
    color: var(--color_font_medium)
}

.comparatorTabs ul,.flex,.page-contact .cols,.page-contact .page-content .box-captcha {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.flex-column {
    -webkit-box-orient: vertical;
    -ms-flex-direction: column;
    flex-direction: column
}

.justify-between {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.justify-around {
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.justify-center {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.justify-start {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.justify-end {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.align-start {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.align-center,.align-content-center {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.align-end {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
}

.justify-self-end {
    -ms-grid-column-align: end;
    justify-self: end
}

.align-self-center {
    -ms-flex-item-align: center;
    -ms-grid-row-align: center;
    align-self: center
}

.align-self-start {
    -ms-flex-item-align: start;
    align-self: flex-start
}

.align-self-end {
    -ms-flex-item-align: end;
    align-self: flex-end
}

.align-self-base {
    -ms-flex-item-align: baseline;
    align-self: baseline
}

.f-wrap {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.flex-grow {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1
}

.flex-1,.flex-2 {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.container {
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 .938rem
}

*,.cart section#cart .custom-showcase-cart .product .actions,.modal-theme.modal-video .modal-info,.modal-theme.product-ruler-modal .modal-wrapper>.modal-info,.page-content .container .container,.sr-only,p {
    padding: 0
}

.row {
    margin: 0 -.938rem
}

.row .col {
    padding: 0 .938rem;
    display: block
}

.row .col.s11 {
    width: 91.6666666667%
}

.row .col.s10 {
    width: 83.3333333333%
}

.row .col.s9 {
    width: 75%
}

.row .col.s8 {
    width: 66.6666666667%
}

.row .col.s7 {
    width: 58.3333333333%
}

.row .col.s6 {
    width: 50%
}

.row .col.s5 {
    width: 41.6666666667%
}

.row .col.s4 {
    width: 33.3333333333%
}

.row .col.s3,.template-instagram:not(.active-slide) .item.swiper-slide {
    width: 25%
}

.row .col.s2 {
    width: 16.6666666667%
}

.row .col.s1 {
    width: 8.3333333333%
}

@media(min-width: 601px) {
    .row .col.m12 {
        width:100%
    }

    .row .col.m11 {
        width: 91.6666666667%
    }

    .row .col.m10 {
        width: 83.3333333333%
    }

    .row .col.m9 {
        width: 75%
    }

    .row .col.m8 {
        width: 66.6666666667%
    }

    .row .col.m7 {
        width: 58.3333333333%
    }

    .row .col.m6 {
        width: 50%
    }

    .row .col.m5 {
        width: 41.6666666667%
    }

    .row .col.m4 {
        width: 33.3333333333%
    }

    .row .col.m3 {
        width: 25%
    }

    .row .col.m2 {
        width: 16.6666666667%
    }

    .row .col.m1 {
        width: 8.3333333333%
    }
}

@media(min-width: 993px) {
    .row .col.l12 {
        width:100%
    }

    .row .col.l11 {
        width: 91.6666666667%
    }

    .row .col.l10 {
        width: 83.3333333333%
    }

    .row .col.l9 {
        width: 75%
    }

    .row .col.l8 {
        width: 66.6666666667%
    }

    .row .col.l7 {
        width: 58.3333333333%
    }

    .row .col.l6 {
        width: 50%
    }

    .row .col.l5 {
        width: 41.6666666667%
    }

    .row .col.l4 {
        width: 33.3333333333%
    }

    .row .col.l3 {
        width: 25%
    }

    .row .col.l2 {
        width: 16.6666666667%
    }

    .row .col.l1 {
        width: 8.3333333333%
    }
}

@media(min-width: 1201px) {
    .row .col.x12 {
        width:100%
    }

    .row .col.x11 {
        width: 91.6666666667%
    }

    .row .col.x10 {
        width: 83.3333333333%
    }

    .row .col.x9 {
        width: 75%
    }

    .row .col.x8 {
        width: 66.6666666667%
    }

    .row .col.x7 {
        width: 58.3333333333%
    }

    .row .col.x6 {
        width: 50%
    }

    .row .col.x5 {
        width: 41.6666666667%
    }

    .row .col.x4 {
        width: 33.3333333333%
    }

    .row .col.x3 {
        width: 25%
    }

    .row .col.x2 {
        width: 16.6666666667%
    }

    .row .col.x1 {
        width: 8.3333333333%
    }
}

.uppercase {
    text-transform: uppercase
}

.user-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.transition {
    transition: .3s ease-in-out
}

.t-scale {
    -webkit-transition: -webkit-transform .3s cubic-bezier(.53,.01,.36,1.63)!important;
    transition: transform .3s cubic-bezier(.53,.01,.36,1.63)!important;
    -o-transition: transform .3s cubic-bezier(.53,.01,.36,1.63)!important;
    transition: transform .3s cubic-bezier(.53,.01,.36,1.63),-webkit-transform .3s cubic-bezier(.53,.01,.36,1.63)!important
}

.t-color {
    transition: color .2s ease-in-out
}

.t-bg {
    transition: background-color .2s ease-in-out
}

.t-opacity {
    transition: opacity .2s ease-in-out
}

.photo-rounded:before,.photo-square:before,.square:after {
    content: "";
    display: block;
    padding-bottom: 100%
}

.photo-rounded img,.photo-square img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%
}

.off-margin-top {
    margin-top: 0!important
}

.off-margin-bottom,.off-margin-left {
    margin-bottom: 0!important
}

.off-margin-right {
    margin-right: 0!important
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    clip: rect(0,0,0,0);
    border: 0
}

.modal,.modal-backdrop {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0
}

.item-image img.swiper-lazy,.modal-backdrop.fade,.page-contact .page-content,img:not([src]) {
    opacity: 0
}

img.swiper-lazy {
    opacity: 0;
    transition: .2s ease-in-out
}

.swiper-container-fade .swiper-slide.swiper-slide-active {
    opacity: 1!important
}

.swiper-container-fade .swiper-slide {
    opacity: 0!important
}

.modal-backdrop {
    z-index: 1040;
    background-color: #000
}

.modal-backdrop.in {
    opacity: .5
}

.modal {
    z-index: 1050;
    -webkit-overflow-scrolling: touch
}

.modal.fade .modal-dialog {
    transition: transform .3s ease-out;
    transform: translateY(-25%)
}

.modal.in .modal-dialog {
    -webkit-transform: translate(0);
    -ms-transform: translate(0);
    transform: translate(0)
}

.modal-open .modal {
    padding-left: 0!important;
    padding-right: 0!important;
    background: rgb(0 0 0 / 34%)
}

.modal .close {
    font-size: 25px;
    position: absolute;
    top: -20px;
    right: -20px;
    z-index: 10;
    cursor: pointer;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    box-shadow: 0 3px 5px rgba(0,0,0,.15)
}

.modal .close span {
    display: flex;
    align-content: center;
    justify-content: center;
    margin: auto;
    height: 40px
}

.modal .modal-dialog {
    position: relative;
    width: auto;
    margin: 10px
}

.modal .modal-content {
    position: relative;
    outline: 0;
    background-clip: padding-box;
    border: 1px solid var(--color_gray_medium);
    border-radius: 6px;
    box-shadow: 0 3px 9px rgba(0,0,0,.5)
}

.modal .modal-header {
    min-height: 16.43px;
    padding: 15px;
    border-bottom: 1px solid var(--color_gray_medium)
}

.modal .modal-title {
    margin: 0;
    line-height: 1.43;
    padding: 0 2.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
    color: var(--color_font_dark)
}

.modal .modal-body {
    position: relative;
    padding: 15px;
    max-height: 80vh;
    overflow-y: auto;
    scrollbar-color: var(--color_primary_medium) var(--color_gray_medium);
    scrollbar-width: thin
}

.modal .modal-body::-webkit-scrollbar {
    width: 6px;
    background: var(--color_gray_medium)
}

.modal .modal-body::-webkit-scrollbar-track {
    background: var(--color_gray_medium)
}

.modal .modal-body::-webkit-scrollbar-thumb {
    background: var(--color_primary_medium);
    border-radius: 5px
}

.cart-preview-item td {
    padding: 0;
    padding-top: 12px!important;
    padding-bottom: 12px!important;
    border: 0
}

.modal .cart-preview-item-image-box {
    width: 60px;
    padding-left: 10px!important;
    padding-right: 10px!important;
    vertical-align: middle
}

.cart-preview-item-image-quantity-box {
    width: 60px;
    min-height: 60px
}

.cart-preview-quantity-tag-box {
    background: #d3d1d1;
    color: #fff;
    font-size: 12px;
    border-radius: 16px;
    font-weight: 700;
    text-align: center;
    display: block;
    min-width: 16px;
    height: 16px;
    line-height: 16px;
    padding: 5px;
    position: absolute;
    margin-left: 46px;
    margin-top: -13px
}

.cart-preview-item-image-space-box,.cart-preview-quantity-tag-box {
    box-sizing: content-box;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box
}

.cart-preview-item-image-space-box {
    width: 50px;
    height: 50px;
    padding: 4px;
    border: 1px solid #d3d1d1
}

.cart-preview-item-image {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    max-width: 50px!important;
    max-height: 50px!important
}

.cart-preview-item-name-box {
    padding-left: 10px!important;
    text-align: left;
    vertical-align: middle
}

.cart-preview-item-name-box .cart-preview-item-name {
    font-weight: 500;
    color: var(--color_font_dark);
    display: block;
    font-size: .87rem
}

.cart-preview-item-price-box {
    width: 115px;
    font-size: .87rem;
    text-align: right;
    vertical-align: middle
}

.cart-preview-item-price-box small {
    font-size: .61rem;
    display: block
}

.cart-preview-item-delete-box {
    width: 60px;
    padding-right: 10px!important;
    text-align: right;
    vertical-align: middle
}

.cart-preview-item-delete {
    font-size: .75rem;
    cursor: pointer;
    display: block;
    margin-left: 5px
}

.modal .modal-footer {
    padding: 15px 15px 30px;
    text-align: right;
    border-top: 1px solid var(--color_gray_medium)
}

.modal .modal-footer .cart-preview-subtotal {
    margin-bottom: 15px;
    font-size: .87rem
}

.modal.cart-preview .modal-content .currency {
    text-decoration: none
}

.modal .modal-footer .botao-continuar-comprando {
    float: left;
    cursor: pointer
}

.modal-footer .btn+.btn {
    margin-bottom: 0;
    margin-left: 5px
}

.modal-footer .btn-group .btn+.btn {
    margin-left: -1px
}

.modal-footer .btn-block+.btn-block,.pagination .page:first-child {
    margin-left: 0
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

.MapaSite,.comparator,.suggestion {
    overflow: auto
}

.modal-dialog-center {
    margin: 0;
    position: absolute!important;
    top: 50%;
    left: 50%;
    max-width: 92%
}

.modal-body #Page {
    width: auto!important;
    display: block!important
}

#wrapper,.botao-nao_indisponivel,body {
    padding: 0!important
}

* {
    margin: 0;
    box-sizing: border-box
}

body {
    min-width: 360px;
    font-size: 16px;
    line-height: 1.4;
    -webkit-font-smoothing: antialiased
}

#ProdBlock,.compare-hidden,.list-product-countdown.hidden,.modal-backdrop.in,.page-navegacao_visitados .board.BoxVisitados,.page-navegacao_visitados .vitrineVisitados .botao-commerce,.products-history .ValoresLista img[src*=sobconsulta],.tray-hide,[hidden],body.user-is-logged .cart .cart__user-actions,div[data-render="snippets/cart_preview"],div[data-render="snippets/cart_preview"] .modal.cart-preview {
    display: none!important
}

ol,ul {
    list-style: none
}

a {
    text-decoration: none;
    color: inherit
}

table {
    border-spacing: 0
}

.clear {
    clear: both
}

.clear-content:after {
    content: "";
    display: block
}

input:not([type=radio]):not([type=checkbox]),select,textarea {
    font-family: inherit;
    -webkit-appearance: none;
    font-weight: 400;
    background: var(--color_gray_medium);
    border-radius: var(--border_radius_buttons)
}

input::-ms-clear {
    display: none
}

input:not([type=radio]):not([type=checkbox])::placeholder,textarea::placeholder {
    color: var(--color_font_medium);
    opacity: 1
}

.product-wrapper .product-form #quant:focus,.product-wrapper .product-form .product-shipping .input:focus,input:not([type=radio]):not([type=checkbox]):focus,textarea:focus {
    box-shadow: inset 0 0 3px var(--color_gray_dark)
}

select::-ms-expand {
    display: none
}

input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input[type=number] {
    -moz-appearance: textfield!important
}

img.transform {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%,-50%,0);
    opacity: 0;
    transition: .2s ease-out
}

img.transform.horizontal {
    height: 100%
}

#ProdBlock.prodBox,#letMeKnow img,.Mapa,.bottom .bLeft,.bottom .bRight,.carrinho-tabs,.hidden,.leftCorner,.modal {
    display: none
}

.sec_texto {
    color: #999
}

.sec_baixa {
    color: red
}

.sec_media {
    color: #f60
}

.sec_alta {
    color: green
}

.safe {
    margin-bottom: -5px
}

#aviso_depoimento {
    margin: 10px 0;
    text-align: center
}

#preco_atual[value="0.00"]+#preco,.banner-bottom {
    margin-top: 30px
}

.banner-bottom img {
    display: block;
    max-width: 100%;
    margin: auto
}

.MapaSite h2,.bottom,.container,.container2,.suggestion-products,.suggestion-products .suggestion-product a:hover,.suggestion-products .suggestion-product:hover,.topBorder {
    background: 0
}

.container2,.container3 {
    float: none;
    background: 0;
    position: static
}

#modal-form-content fieldset,.container3,.line,.page-busca_noticias hr,button,fieldset,input,li,p,select,textarea,ul {
    border: 0
}

.line {
    height: auto;
    width: auto;
    position: relative
}

.modal-header {
    min-height: 46px
}

#div_atualiza>p:first-of-type,#produto_comprar,.botao-compre-junto:not(.botao-sob-consulta) .botao-commerce-img,.catalog-header .catalog-info .catalogo-form-filtros label:not(:first-child),.comprejunto_preco2 strong:first-child,.obriga-barra.red,.obriga.red,.page-depoimentos .board .left,.page-navegacao_visitados .vitrineVisitados span.txt-corparcelas .preco-parc2,.precoCompreJunto>div:first-child,.product-wrapper .product-form #quantidade label,form[action*=question] fieldset,form[action*=question] fieldset p {
    font-size: 0
}

.obriga-barra.red:before {
    content: "*";
    font-size: 16px
}

.botao-calcular-frete,.botao-calcular-frete:hover,.botao-cupom-desconto,.botao-cupom-desconto:hover,.botao-efetuar-login,.botao-efetuar-login:hover,.botao-enviar-cadastro,.botao-enviar-cadastro:hover,.botao-enviar-cartao,.botao-enviar-cartao:hover,.botao-finalizar-compra,.botao-finalizar-compra:hover,.botao-novo-cadastro,.botao-novo-cadastro:hover,.botao-prosseguir-cadastro,.botao-prosseguir-cadastro:hover,.botao-prosseguir-compra,.botao-prosseguir-compra:hover,.botao-salvar-lista,.botao-salvar-lista:hover,.botao-simular-frete,.botao-simular-frete:hover,a.botao-calcular-frete,a.botao-cupom-desconto,a.botao-efetuar-login,a.botao-enviar-cadastro,a.botao-enviar-cartao,a.botao-finalizar-compra,a.botao-novo-cadastro,a.botao-prosseguir-cadastro,a.botao-prosseguir-compra,a.botao-salvar-lista,a.botao-simular-frete {
    background: unset;
    border: unset;
    color: unset
}

#loading-product-container {
    z-index: 6;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

#loading-product-container:before {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    opacity: .6
}

.botao-commerce:not(.botao-sob-consulta):not(.botao-nao_indisponivel) {
    padding: .625rem 1.25rem;
    font-family: var(--font_family);
    font-size: .75rem;
    font-weight: 700;
    transition: .2s ease-out;
    text-transform: uppercase;
    border-radius: var(--border_radius_buttons);
    background: var(--color_button_buy_bg);
    color: var(--color_button_buy_text)
}

#acceptTerm:hover,#coments .submit-review:hover,#coments a[data-logged-user=false]:hover,#letMeKnow:hover:before,.account a:hover,.actions .product-button:hover,.botao-commerce:hover,.botao-compre-junto:not(.botao-sob-consulta):hover,.box-noticia #noticia_dados .button-show:hover,.box-noticia #noticia_dados h3 a:hover,.breadcrumb a:hover,.bts2:hover,.bts:hover,.footer .footer-main .newsletter .form .news-button:hover,.footer .footer-main .social-media a:hover,.footer .list a:hover,.footer .social-media .icon:hover,.header .cart-toggle:hover,.lista-produtos .lista-produto-comprar:hover,.nav .first-level:hover>a,.nav .second-level>ul.categories-children>li:hover>a,.nav .third-level>ul.categories-children>li:hover>a,.pagination .page a:hover,.perguntasProdutoBTimg:hover,.product-rating .total:hover,.product-wrapper .product-form .product-not-sale .botao_tire_duvidas:hover:before,.product-wrapper .product-form .product-shipping .submit-shipping:hover,.smart-filter .filter-button:hover {
    opacity: .8
}

#wrapper {
    transform: inherit!important
}

.brinde_lista {
    display: flex;
    flex-wrap: wrap;
    width: auto!important;
    margin: 0 -.3125rem;
    justify-content: center
}

.brinde_lista img {
    border: 2px solid #fff!important
}

.brinde_lista img[style*="border: 2px"] {
    box-shadow: 0 0 0 2px var(--color_primary_medium)
}

.brinde_lista span {
    display: block;
    cursor: pointer
}

.brinde_lista li {
    margin-top: 5px;
    padding: 0 5px
}

.brinde_detalhes .botao {
    position: relative;
    display: inline-block;
    cursor: pointer;
    z-index: 1
}

.brinde_detalhes .botao:before {
    content: "ESCOLHER";
    display: block;
    width: 100px;
    color: #fff;
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    line-height: 40px;
    text-align: center;
    font-size: .75rem;
    background-color: var(--color_secondary_medium)
}

.brinde_detalhes .botao img {
    opacity: 0;
    display: block;
    width: 100px;
    height: 40px
}

#container-add-lista .listas a,.account span,.catalog-footer .results,.varTit {
    font-size: .875rem;
    font-weight: 500
}

#depoimento label h3,.varTit * {
    font-size: inherit;
    font-weight: inherit
}

.load-css .icon {
    left: 50%;
    top: 50%;
    position: absolute;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    width: 30px;
    height: 30px
}

.load-css .icon:before {
    border: 3px solid #9c9c9c;
    border-radius: 50%
}

.load-css .icon:after,.load-css .icon:before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%
}

.comparator .comparsionFoto .closeComp,.comparatorTabs ul li a:last-child {
    position: absolute;
    right: 5px;
    width: 22px;
    height: 22px;
    color: var(--color_font_inverted);
    font-weight: 700;
    top: 0;
    line-height: 22px;
    text-align: center
}

.load-css .icon:after {
    border: 3px solid #000;
    border-right-color: transparent;
    animation: .6s linear infinite rotate;
    border-radius: 50%
}

@keyframes rotate {
    0 {
        transform: rotate(0)
    }

    to {
        transform: rotate(1turn)
    }
}

#letMeKnow:before {
    content: "Avise-me";
    display: inline-block;
    background: var(--color_secondary_medium);
    line-height: 52px;
    padding: 0 30px;
    color: #fff;
    font-weight: 400;
    height: 52px;
    vertical-align: bottom;
    border-radius: 0 3px 3px 0;
    width: 160px;
    text-align: center;
    transition: .2s ease-out
}

.botao-nao_indisponivel {
    background: 0 0!important;
    font-size: 0!important
}

#nao_disp {
    font-size: .875rem;
    border-radius: var(--border_radius_buttons)
}

#nao_disp h3 {
    font-size: 1rem;
    font-weight: 600
}

#nao_disp h5,.contato-telefones .block,.page-comparador .page-content>.container>.board>.container3>.container2>.board .left strong,.page-listas_busca h2~ul li .NomeProduto+label b,.product-wrapper .product-form .product-shipping .shipping-rates-table td strong {
    font-weight: 400
}

#nao_disp h5 {
    font-size: .875rem;
    margin: 5px 0 0
}

#nao_disp .color {
    font-size: .75rem;
    margin-top: 10px;
    display: none
}

#letmeknow_response label,#nao_disp label {
    display: inline-block;
    width: calc(100% - 186px);
    margin-top: 23px;
    max-width: 228px
}

#email_avise {
    display: block;
    width: 100%;
    border-radius: 0
}

button {
    cursor: pointer
}

.campoform:not([type=checkbox]),input.text,select.select,select.text {
    padding: 0 1.5rem;
    height: 52px;
    font-size: .875rem;
    line-height: 1.4;
    color: var(--color_font_medium);
    border: 1px solid var(--color_gray_dark);
    border-radius: var(--border_radius_buttons)
}

input.text.obriga,select.obriga {
    padding-left: 17px
}

input.text:disabled,select.select:disabled,select.text:disabled {
    background-color: var(--color_gray_medium)!important
}

select.select,select.text {
    -webkit-appearance: none;
    background-repeat: no-repeat;
    background-image: var(--arrow_select)!important;
    background-size: 9.2px;
    background-position: calc(100% - 10px) 50%!important;
    padding-right: 22px!important;
    cursor: pointer
}

select.select::-ms-expand,select.text::-ms-expand {
    display: none
}

textarea#mensagem,textarea.textarea {
    border: 1px solid var(--color_gray_dark);
    padding: .875rem;
    font-size: .875rem;
    border-radius: var(--border_radius_images);
    color: var(--color_font_medium)
}

.blocoSucesso {
    color: #00ad29;
    margin: 10px 0;
    background: #f1f9f3;
    padding: .5rem;
    font-size: .75rem;
    text-align: center;
    border-radius: var(--border_radius_images)
}

form[action*=question] .text,form[action*=question] .textarea {
    resize: none;
    display: block;
    max-width: 384px;
    margin: 9px auto 0
}

#NavLogoTray .timelogotray,#NavLogoTray br,#ProdBlockCompreJunto #ProdAbas,#alert-show,#alert-show+.box-alerts,#alert-show+.infobox.danger,#alert-show:checked~.box-alerts,#coments br,#depoimento br,#enviar_dep,#lightwindow,#lightwindow_overlay,#preco_atual[value="0.00"]+#preco br:first-of-type,#produto_comprar a img,#produto_dados p,#visitados_itens,.Seguro,.account [data-logged-user=true]:not(.tray-hide)+[data-logged-user],.box-alerts:empty,.btn-primeira-pagina,.btn-ultima-pagina,.btns-paginator img,.catalog-header .catalog-info .catalogo-form-filtros label.filtro-ordem+label,.clearVisiteds,.close-info:first-child,.col-panel .icoPai img,.col-panel hr,.comparator .comparsionFoto a[alt=Comprar] img,.comparator .comparsionFoto a[title=Escolher] img,.comparsionInfoPreco br+br,.compreJunto .unidades_topo,.compreJunto .varCont+.varTit:not(.onVar),.dep_dados .dep_data,.dep_dados .dep_msg span,.dep_dados .dep_nome span,.dep_link,.dep_nota,.editDep ul li strong,.editDep ul li:not(.dep):first-child,.editDep ul li:nth-child(2),.header .logo img+.title-store,.header .logo svg+.title-store,.header-info,.modal-theme .light_altura hr,.nav-mobile li.sub>.second-level,.page-busca_noticias .Seguro~hr~*,.page-busca_noticias:not(.show-menu) .menu-mobile,.page-busca_noticias:not(.show-menu) .nav .second-level,.page-contact .page-content .cols~*,.page-contact .page-content label.block>span:not([class]),.page-contact input[type=image],.page-depoimentos .page-content #depoimento,.page-depoimentos .page-content br,.page-depoimentos .page-content h2:first-of-type,.page-depoimentos .page-content h2:last-of-type,.page-depoimentos .page-content hr,.page-depoimentos:not(.show-menu) .menu-mobile,.page-depoimentos:not(.show-menu) .modal-store-reviews,.page-depoimentos:not(.show-menu) .nav .second-level,.page-listas_busca h2~ul br,.page-login .carrinho-heading,.page-login .titulo-login,.page-navegacao_visitados .change,.page-newsletter .formulario-newsletter p:first-child br,.page-newsletter .page-content.success-message-newsletter .board p:first-child br,.page-noticia .dataNoticia,.product .product-price .product-installments br:first-child,.product .product-price .product-installments span.txt-com-desconto,.product .product-price .product-installments span.txt-forma-pagamento,.product-tabs .tabs-content .tab,.product-tabs .tabs-content .tab-link-mobile,.product-wrapper .product-form .product-additional-message br,.product-wrapper .product-form .product-gifts a#btmais,.product-wrapper .product-form .product-gifts a#btmenos,.product-wrapper .product-form .product-not-sale .produto-formas-pagamento,.product-wrapper .product-form .product-price-tray #info,.product-wrapper .product-form .product-price-tray #produto_preco #info_preco span.txt-com-desconto,.product-wrapper .product-form .product-price-tray #produto_preco #info_preco span.txt-forma-pagamento,.product-wrapper .product-form .product-price-tray .produto-economize,.product-wrapper .product-form .product-price-tray .txt-por,.product-wrapper .product-form .product-price-tray br,.product-wrapper .product-form .product-shipping .produto-calcular-frete,.product-wrapper .product-form [style="clear:both;"],.product-wrapper .product-form span#estoque_variacao,.product-wrapper .product-gallery .product-thumbs:not(.show-arrows) .next,.product-wrapper .product-gallery .product-thumbs:not(.show-arrows) .prev,.products-history #produtos ul li .ValoresLista span.txt-forma-pagamento,.products-history .ValoresLista .oculta_destaque,.products-history .total_produtos,.separador-paginas,.sidebar-category li:not(.sub) a.sub-filter:after,.suggestion .cl,.suggestion .idp,form[action*=question] fieldset .obriga.red,form[action*=question] fieldset p label:nth-of-type(2),form[action*=question] fieldset p label:nth-of-type(4),form[action*=question] fieldset p label:nth-of-type(5),form[action*=question] fieldset p label:nth-of-type(6) {
    display: none
}

#comentario_cliente .blocoAlerta,#div_atualiza,#vitrine-catalogo .blocoAlerta,.cart__title,.modal-theme.product-ruler-modal .modal-info,.page-navegacao_visitados .vitrineVisitados .dados,.page-register #frm2 {
    text-align: center
}

#div_atualiza>p:first-of-type:before {
    content: "Preencha os campos abaixo para conhecermos um pouco melhor as suas necessidades!";
    display: block;
    font-size: 1rem
}

form[action*=question] fieldset p label {
    font-size: .875rem;
    font-weight: 700;
    text-align: center;
    display: block;
    margin-top: 23px
}

#form1 input,#form1 select,#form1 textarea,.caixa-cadastro fieldset input {
    width: 100%;
    display: block
}

.MapaSite {
    width: 100%;
    margin: 15px 0
}

p {
    position: static
}

.MapaSite>ul {
    min-width: 600px
}

.board_htm iframe,.lista-imagem img,.lista_presentes td a img,.modal-theme.product-ruler-modal .modal-info img,.page-noticia .board iframe,.rte iframe,.textarea,html.page-acompanhe-seu-pedido .page-content .row.w-100.justify-content-center.align-items-center.default,input[size] {
    max-width: 100%
}

#modal-form-content h3 {
    font-size: 1.125rem
}

.compare-buttons {
    text-align: center;
    font-size: .75rem;
    margin: 6px 0
}

.compare-buttons .filter-checkbox {
    display: flex;
    justify-content: center;
    width: 14px;
    height: 14px;
    border: 1px solid var(--color_gray_dark);
    align-items: center;
    margin-right: 5px
}

.compare-buttons [data-compare=remove] .filter-checkbox:before {
    content: "";
    display: block;
    width: 8px;
    height: 8px;
    background-color: var(--color_primary_medium)
}

@media(min-width: 931px) {
    .catalog-header .catalog-info .system-filter {
        order:3
    }

    .compare-wrapper {
        font-size: .87rem;
        text-transform: uppercase;
        font-weight: 700;
        height: 33px;
        line-height: 33px;
        padding: 0 23px;
        white-space: nowrap;
        background: var(--color_secondary_medium);
        margin-right: 10px;
        color: #fff;
        border-radius: var(--border_radius_buttons);
        order: 2;
        margin-left: auto;
        transition: .2s ease-out
    }

    .compare-wrapper:hover {
        opacity: .8
    }
}

@media(max-width: 930px) {
    .compare-wrapper {
        order:3;
        width: 100%;
        margin-top: 1.25rem
    }

    .button-compare {
        line-height: 40px;
        height: 40px;
        display: block;
        width: 200px;
        margin: 0 auto;
        text-align: center;
        font-size: .75rem;
        text-transform: uppercase;
        font-weight: 700;
        padding: 0 23px;
        white-space: nowrap;
        background: var(--color_secondary_medium);
        color: var(--color_font_inverted);
        border-radius: var(--border_radius_buttons)
    }
}

.comparatorTabs,.page-navegacao_visitados div#vitrine-catalogo>.container3:not(.central-conteudo) .container2 {
    margin: 15px 0
}

.comparatorTabs ul li {
    position: relative;
    margin-right: 5px
}

.comparatorTabs ul li a:first-child {
    padding: 5px 30px 5px 10px;
    display: block
}

.comparatorTabs ul li a:last-child {
    bottom: 0;
    margin: auto;
    border-radius: 50%;
    background-color: var(--color_secondary_medium);
    border: 0;
    font-size: 13px
}

.comparatorTabs ul li.aberta a:first-child {
    background-color: var(--color_secondary_medium);
    color: #fff;
    border-radius: var(--border_radius_buttons)
}

.comparator {
    width: 100%;
    margin-bottom: 20px
}

.comparator ul ul {
    display: table;
    table-layout: fixed
}

.comparator ul ul>li {
    display: table-cell;
    width: 150px;
    padding: 10px;
    border: 1px solid var(--color_gray_dark);
    border-top: 0
}

.comparator ul .Labels ul>li {
    border-top: 1px solid var(--color_gray_dark);
    font-weight: 600
}

.comparator ul ul>li.comparsionFoto,.comparator ul ul>li.comparsionInfoPreco {
    width: 250px
}

.comparator ul ul>li.comparsionRate,.comparator ul ul>li.displayDisponibilidade {
    width: 180px
}

.comparator ul ul>li.comparsionDescricao {
    width: 190px
}

.comparator ul ul>li.displayMarca {
    width: 120px
}

.comparator ul ul>li.displayGarantia,.comparator ul ul>li.displayModelo {
    width: 140px
}

.comparator .comparsionFoto a {
    font-size: .875rem;
    line-height: 1.2;
    display: block;
    margin: 0 0 10px;
    max-height: 142px;
    overflow: hidden
}

.comparator .comparsionFoto a[alt=Comprar]:before,.comparator .comparsionFoto a[title=Escolher]:before {
    content: "COMPRAR";
    display: inline-block;
    line-height: 34px;
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    cursor: pointer;
    padding: 0 10px;
    text-transform: uppercase;
    font-size: .75rem;
    font-weight: 600;
    border-radius: 2px
}

.comparator .comparsionFoto a[title=Escolher]:before {
    content: "ESCOLHER"
}

.comparator .comparsionFoto .closeComp {
    font-size: .75rem;
    display: block;
    background-color: #fff;
    border-radius: 50%;
    background-color: var(--color_secondary_medium);
    border: 0;
    margin-top: 10px
}

.page-comparador h1 {
    color: var(--color_font_dark);
    font-weight: 600;
    font-size: 28px;
    margin-bottom: 30px;
    text-align: center
}

.asas:after,.page-comparador h1:after {
    content: "";
    display: block;
    width: 62px;
    height: 4px;
    margin: 15px auto 0;
    background-color: var(--color_primary_medium)
}

.page-comparador .page-content>.container>.board>.container3>.container2>.board .left {
    font-size: 14px;
    font-weight: 400
}

.block-custom .block-custom__content:not(.swiper-wrapper),.page-comparador .page-content>.container>.board>.container3>.container2>.board {
    display: flex;
    align-items: center;
    justify-content: space-between
}

.page-comparador .page-content>.container>.board>.container3>.container2>.board .right,.page-login .page-content .board,.page-navegacao_visitados .vitrineVisitados span.txt-corparcelas span.txt-cadaparcelas,.page-navegacao_visitados .vitrineVisitados span.txt-corparcelas span.txt-corparcelas,.page-navegacao_visitados .vitrineVisitados span.txt-corparcelas strong.preco-parc2,.page-newsletter .formulario-newsletter .box-captcha,.page-newsletter .formulario-newsletter .box-captcha-newsletter,.products-history .history-loader.show {
    display: flex
}

.page-comparador .page-content>.container>.board>.container3>.container2>.board .right>* {
    min-width: 44px;
    height: 38px
}

.box-shadow {
    box-shadow: 0 4px 8px rgba(0,0,0,.04)
}

.box-alerts {
    padding: 5px 29px;
    font-size: .75rem;
    color: #fff;
    position: fixed;
    bottom: 0;
    width: 100%;
    left: 0;
    right: 0;
    background: #a51111;
    text-align: center;
    z-index: 111111
}

.close-info {
    position: absolute;
    top: 0;
    width: 14px;
    height: 14px;
    cursor: pointer;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    right: 10px;
    bottom: 0;
    margin: auto;
    display: block
}

.close-info:before {
    width: 14px;
    height: 2px;
    top: 6px
}

.close-info:after,.close-info:before {
    content: "";
    display: block;
    background: #1e201d;
    position: absolute
}

.close-info:after {
    height: 14px;
    width: 2px;
    left: 6px
}

@media(max-width: 395px) {
    .infobox.danger+.close-info {
        top:32px
    }
}

@media(max-width: 431px) and (min-width:395px) {
    .infobox.danger+.close-info {
        top:-16px
    }
}

@media(max-width: 802px) and (min-width:431px) {
    .infobox.danger+.close-info {
        top:18px
    }
}

.infobox.danger+.close-info:after,.infobox.danger+.close-info:before {
    background: #fff
}

.box-alerts .infobox.danger {
    font-size: .75rem;
    padding: unset;
    color: #fff
}

.box-alerts .infobox {
    font-size: .75rem;
    padding: 10px 30px 10px 10px;
    color: #707070
}

.title-store {
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center
}

.perguntasProdutoBTimg {
    float: none!important;
    margin: 1.25rem auto 0;
    display: block;
    width: 160px!important;
    height: 42px!important;
    font-size: .875rem;
    font-weight: 600;
    border-radius: 2px;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    text-transform: uppercase;
    transition: .2s ease-out;
    border-radius: var(--border_radius_buttons)
}

.board_htm,.rte {
    width: 100%;
    overflow: hidden;
    font-family: inherit;
    font-size: 1rem;
    line-height: 25px
}

.board_htm:after,.page-noticia .board:after {
    content: "";
    display: block;
    clear: both
}

.board_htm h1,.page-noticia .board h1,.rte h1 {
    font-size: 1.675rem;
    font-weight: 700;
    margin-bottom: 16px
}

.board_htm h2,.page-noticia .board h2,.rte h2 {
    font-size: 1.425rem;
    font-weight: 700;
    margin-bottom: 16px
}

.board_htm h3,.page-noticia .board h3,.rte h3 {
    font-size: 1.35rem;
    font-weight: 700;
    margin-bottom: 16px
}

.board_htm h4,.page-noticia .board h4,.rte h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 16px
}

.board_htm h5,.page-noticia .board h5,.rte h5 {
    font-size: 1.1125rem;
    font-weight: 700;
    margin-bottom: 16px
}

.board_htm h6,.page-noticia .board h6,.rte h6 {
    font-size: 1rem;
    margin-bottom: 16px;
    font-weight: 700
}

.board_htm img,.rte img {
    max-width: 100%;
    height: auto!important;
    margin: 10px 0
}

.board_htm img[style*="float: right"],.rte img[style*="float: right"] {
    margin: 10px 0 10px 10px
}

.board_htm img[style*="float: left"],.rte img[style*="float: left"] {
    margin: 10px 10px 10px 0
}

.board_htm p,.rte p {
    margin-bottom: 14px
}

.board_htm p:last-child,.cart section#cart .custom-showcase-cart .section-header,.rte p:last-child {
    margin-bottom: 0
}

.board_htm ul,.page-noticia .board ul,.rte ul {
    list-style: disc;
    margin-left: 1.875rem;
    margin-bottom: 1.25rem;
    line-height: 1.7
}

.board_htm ol,.page-noticia .board ol,.rte ol {
    list-style: decimal;
    margin-left: 1.875rem;
    margin-bottom: 1.25rem;
    line-height: 1.7
}

.board_htm td,.board_htm th,.rte table td,.rte table th {
    padding: .3125rem .625rem
}

.board_htm table tr:nth-child(odd) td,.product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(odd) td,.product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(odd) th,.rte table tr:nth-child(odd) td {
    background-color: rgba(0,0,0,.05)
}

.board_htm table tr:nth-child(2n) td,.product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(2n) td,.product-wrapper .product-form .product-shipping .shipping-rates-table tr:nth-child(2n) th,.rte table tr:nth-child(2n) td {
    background-color: rgba(0,0,0,.02)
}

.board_htm .rte-video-wrapper,.page-noticia .board .rte-video-wrapper,.rte .rte-video-wrapper {
    position: relative;
    overflow: hidden;
    max-width: 100%;
    height: auto;
    padding-bottom: 56.25%
}

.modal-theme,.modal-theme .modal-shadow,.video iframe {
    top: 0;
    width: 100%;
    height: 100%;
    left: 0
}

.board_htm .rte-video-wrapper .iframe,.board_htm .rte-video-wrapper iframe,.page-noticia .board .rte-video-wrapper iframe,.rte .rte-video-wrapper .iframe,.rte .rte-video-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

.video:before {
    content: "";
    display: block;
    padding-bottom: 56.25%
}

.video iframe {
    position: absolute;
    display: block;
    border: 0
}

.modal-theme {
    position: fixed;
    z-index: 30;
    overflow: auto;
    opacity: 0;
    visibility: hidden;
    transition: .3s ease-in-out
}

#container-add-lista #add-listas[style*="display: block;"],.cart.cart-show,.compreJunto .produto img.loaded,.footer .payment-list li img.loaded,.loader.show,.modal-theme.show,.page-busca_noticias .page-content.show,.page-depoimentos .page-content.show,.page-newsletter .page-content.show,.product-wrapper .product-form .product-progressive-discount .tooltip:hover .tooltip-content,.product-wrapper .product-gallery .product-thumbs.show,.product.has-secondary-image .image>a>img:first-child,.product.has-secondary-image:hover .image>a>img:nth-child(2),.section-avaliacoes.show,.section-buy-together .buy-together-loader.show {
    opacity: 1;
    visibility: visible
}

.loader:not(img),.nav .list .second-level,.overlay-shadow,.product.has-secondary-image .image>a>img:nth-child(2),.product.has-secondary-image:hover .image>a>img:first-child {
    visibility: hidden;
    opacity: 0
}

.modal-theme .modal-shadow {
    position: fixed;
    z-index: 1;
    background-color: rgba(0,0,0,.5)
}

.modal-theme .close-icon {
    position: absolute;
    top: -20px;
    right: -20px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    cursor: pointer;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: var(--color_font_medium);
    border-radius: 50%;
    background-color: #fff;
    box-shadow: 0 3px 5px rgba(0,0,0,.15)
}

.modal-theme .close-icon .icon {
    height: 20px;
    display: block
}

.modal-theme .modal-scroll {
    width: 100%;
    height: 100%;
    overflow: auto
}

.modal-theme .modal-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 100%;
    padding: 2.5rem
}

.modal-theme .modal-info {
    position: relative;
    width: 100%;
    min-width: 200px;
    max-width: 1000px;
    padding: 2.5rem;
    color: var(--color_font_medium);
    background: #fff;
    border-radius: 6px;
    transition: transform .3s cubic-bezier(.54,-.65,.48,1.64);
    transform: scale(.6);
    z-index: 2
}

.modal-theme.show:not(.loaded) .modal-info {
    transform: scale(1)
}

.modal-theme .modal-content img {
    display: block;
    max-width: 100%
}

.modal-theme.modal-video .video {
    overflow: hidden;
    border-radius: 6px
}

.nav .list .second-level,.nav-mobile {
    overflow-y: auto;
    scrollbar-width: thin
}

.modal-theme.modal-store-reviews .modal-info {
    max-width: 500px
}

.modal-theme .append {
    padding: 30px 20px 20px
}

.modal-theme .light_altura {
    width: 400px!important;
    max-width: 100%
}

.modal-theme .light_altura h2 {
    font-weight: 600;
    text-transform: uppercase;
    font-size: .875rem
}

@media(min-width: 991px) {
    .modal-theme .append {
        padding:30px 40px
    }
}

.error-message {
    padding: .5rem;
    font-size: .75rem;
    text-align: center;
    border-radius: 4px;
    color: #e15656;
    background-color: #ffebeb
}

.swiper-carousel .next,.swiper-carousel .prev,.swiper-container .next,.swiper-container .prev {
    border: 1px solid #ececec;
    position: absolute;
    top: calc(50% - 20px);
    width: 43px;
    height: 44px;
    cursor: pointer;
    color: #fff;
    z-index: 3;
    border-radius: 50px;
    line-height: 50px;
    text-align: center;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 19px;
    font-weight: 300;
    background: var(--color_secondary_medium);
    background: #fff;
    color: var(--color_secondary_medium)
}

.swiper-carousel .prev,.swiper-container .prev {
    left: 0
}

.swiper-carousel .next,.swiper-container .next {
    right: 0
}

.swiper-carousel .next.swiper-button-disabled,.swiper-carousel .prev.swiper-button-disabled,.swiper-container .next.swiper-button-disabled,.swiper-container .prev.swiper-button-disabled {
    opacity: .2
}

.swiper-container .dots {
    display: flex;
    justify-content: center;
    width: 100%
}

.swiper-container .dots .dot {
    width: 8px;
    margin: 0 5px;
    cursor: pointer;
    padding: 5px 0
}

.swiper-container .dots .dot:after {
    content: "";
    display: block;
    height: 8px;
    border-radius: 4px;
    background-color: var(--color_primary_medium);
    transition: .2s ease-out;
    opacity: .4
}

.section-header {
    text-align: center;
    margin-bottom: 30px
}

.section-header .title-section {
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    color: var(--color_font_dark);
    font-family: var(--font_family_title)
}

.section-header .subtitle-section {
    font-size: 1.125rem;
    font-weight: 500;
    text-align: center;
    color: var(--color_primary_medium)
}

.loader .message,.suggestion-title {
    color: var(--color_font_dark);
    font-size: .875rem
}

@media(min-width: 992px) {
    .modal-lg {
        width:900px
    }

    .page-catalog .page-content,.page-content:not(.not-padding),.page-search .page-content {
        padding-top: 15px
    }
}

.suggestion {
    position: absolute;
    top: 100%;
    left: 0;
    max-width: unset;
    max-height: 450px;
    min-width: unset;
    width: 100%;
    padding: 0 1.25rem;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,.16);
    background-color: #fff;
    border: 0;
    border-radius: 0 0 3px 3px;
    scrollbar-color: var(--color_header_highlight) #fff;
    scrollbar-width: thin
}

.suggestion::-webkit-scrollbar {
    width: 6px;
    background: #fff
}

.suggestion::-webkit-scrollbar-track {
    background: #fff
}

.suggestion::-webkit-scrollbar-thumb {
    background: var(--color_header_highlight);
    border-radius: 5px
}

.suggestion-words span,.suggestion-words span:hover {
    background-color: transparent
}

.suggestion:hover {
    display: block!important
}

.suggestion-title {
    font-family: inherit;
    line-height: 1.4;
    font-weight: 600;
    text-indent: 0;
    margin-bottom: 10px;
    text-transform: uppercase;
    background-color: transparent!important
}

.cart__empty,.cart__product__name,.cart__product__price,.cart__title,.cart__total-price__value {
    font-family: var(--fonte-especial);
    text-transform: var(--fonte-especial-transform)
}

.suggestion-words {
    margin-bottom: 1.875rem;
    padding: 1.25rem 0 0;
    background: 0
}

.suggestion-words .suggestion-title {
    margin-bottom: .3125rem
}

.suggestion-words span {
    font-size: .875rem;
    border: 0;
    margin: 0;
    padding: 0;
    line-height: 1.4;
    transition: .2s ease-out
}

.suggestion-products {
    width: 100%;
    padding: 0
}

.suggestion-products .suggestion-product a {
    display: flex;
    margin: 0;
    padding: 0;
    align-items: center
}

.suggestion-products .suggestion-product .suggestion-img {
    width: 50px;
    height: 60px;
    float: unset;
    margin-right: .625rem
}

.suggestion-products .suggestion-product .suggestion-img img {
    width: 100%;
    height: 100%;
    object-fit: cover
}

.suggestion-products .suggestion-product .suggestion-desc {
    width: calc(100% - 60px)
}

.suggestion-products .suggestion-product .suggestion-desc .titlep {
    font-size: .875rem;
    font-weight: 500;
    color: var(--color_primary_medium);
    transition: .2s ease-out
}

.loader:not(img) {
    position: absolute;
    z-index: 5;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: hsla(0,0%,100%,.8);
    transition: .2s ease-out
}

.loader .spinner {
    width: 40px;
    height: 40px;
    position: relative
}

.loader .spinner .double-bounce-one,.loader .spinner .double-bounce-two {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: var(--color_primary_medium);
    opacity: .6;
    position: absolute;
    top: 0;
    left: 0;
    animation: 2s ease-in-out infinite sk-bounce
}

.loader .spinner .double-bounce-two {
    animation-delay: -1s
}

#container-add-lista .listas+.listas,.loader .message,.page-listas_busca form[action*="listas_busca.php"] {
    margin-top: .625rem
}

@keyframes sk-bounce {
    0,to {
        transform: scale(0)
    }

    50% {
        transform: scale(1)
    }
}

.floating-whatsapp {
    position: fixed;
    display: flex;
    bottom: 70px;
    font-size: 1rem;
    z-index: 9
}

.floating-whatsapp.on-left {
    left: 50px
}

.floating-whatsapp.on-right {
    right: 50px
}

.floating-whatsapp a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    transition: .2s ease-out;
    color: #fff;
    background-color: #25d366
}

.floating-whatsapp a:hover {
    background-color: #23c35f
}

.floating-whatsapp a .icon {
    font-size: 2.0625rem
}

.overlay-shadow {
    position: fixed;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 19;
    background-color: #000;
    transition: .3s ease-out
}

.overlay-shadow.show {
    opacity: .5;
    visibility: visible
}

.close-box {
    position: absolute;
    top: 2.8125rem;
    right: 3.625rem;
    font-size: 1.125rem;
    width: 22px;
    height: 22px;
    cursor: pointer
}

.close-box:hover .icon {
    transition: .5s ease-in-out;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.header-search-wrapper {
    position: relative;
    width: 100%;
    margin: 0 30px
}

header .header-search-wrapper .input-search {
    display: block;
    width: 100%;
    height: 46px;
    padding: 0 58px 0 20px;
    font-weight: 400;
    color: var(--color_header_search_text);
    background: var(--color_header_details);
    box-shadow: none!important;
    border-radius: var(--border_radius_buttons)
}

.header-info .header-info__content {
    padding: 9px 0 3px;
    justify-content: space-between
}

.footer .copy .tray .credits-partner,.header-info .header-info__content li {
    margin-right: 15px;
    padding-right: 15px;
    border-right: 1px solid #eee
}

.header-info .header-info__content .social-media a {
    padding: 0 5px;
    color: var(--color_primary_medium);
    font-size: 18px
}

.footer .footer-main .newsletter .form .field:focus,header .header-search-wrapper .input-search:focus {
    box-shadow: inset 0 0 4px #eee
}

header .header-search-wrapper .input-search::placeholder {
    color: currentcolor
}

.header-search-wrapper .button-search {
    position: absolute;
    width: 58px;
    height: 100%;
    top: 1px;
    right: 0;
    font-size: 20px;
    background-color: transparent;
    color: var(--color_header_highlight)
}

.header>.bg {
    background-color: var(--color_header_bg)
}

.header .logo {
    display: block;
    flex-shrink: 0;
    font-size: 0
}

.header .logo img {
    width: auto;
    height: auto
}

.account>a>i,.contact>i {
    color: var(--color_header_highlight);
    font-size: 25px;
    margin-right: 0!important;
    padding-right: 0;
    cursor: pointer
}

.account div>span {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    min-height: 17px;
    -webkit-box-orient: vertical;
    overflow: hidden;
    display: -webkit-box;
    font-weight: 400;
    line-height: normal;
    font-size: 12px
}

.account .login-links span,.account a,.account div {
    color: var(--color_header_text);
    font-size: .81rem;
    transition: .2s ease-out;
    line-height: 14px
}

.header-wrapper .header-wrapper__content .login-links .login-links__featured {
    background: var(--color_secondary_medium);
    text-align: center;
    padding: 13px 10px;
    color: #fff;
    font-size: 17px;
    border-radius: var(--border_radius_buttons);
    margin-bottom: 10px
}

.header-wrapper .header-wrapper__content .login-links a {
    font-size: 15px;
    padding: 6px 0;
    color: var(--color_font_medium)
}

.account .login-links {
    display: flex;
    flex-wrap: wrap;
    line-height: normal
}

.account .login-links span {
    padding: 0 4px;
    font-weight: 400
}

header .contact i svg {
    width: 29px;
    margin-top: -1px
}

header .account i svg {
    width: 39px
}

header .contact.gift {
    margin-right: 20px
}

.header .cart-toggle .icon {
    color: var(--color_header_highlight);
    display: inline-block;
    margin-right: 6px
}

.header .cart-toggle .cart-quantity {
    display: inline-block;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    line-height: 22px;
    text-align: center;
    font-size: .875rem;
    font-weight: 400;
    vertical-align: middle;
    color: var(--color_header_cart_count_text);
    background-color: var(--color_header_cart_count);
    position: absolute;
    right: -11px;
    top: 10px
}

.header .cart-toggle {
    margin-left: 5px;
    flex-shrink: 0;
    transition: .2s ease-out;
    align-items: center;
    display: flex;
    position: relative;
    margin-top: -4px
}

.menu-mobile .nav-mobile-wrapper,.nav {
    border-top: 1px solid var(--color_header_details)
}

.nav {
    position: relative;
    transition: transform .3s ease-out .3s,background .3s ease-out;
    width: 100%;
    background: var(--color_header_menu_bg)
}

.nav .list>li>a,.nav .list>li>span {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    padding: 5px 12px;
    font-size: 15px;
    font-weight: 700;
    cursor: pointer;
    color: var(--color_header_menu_text);
    z-index: 2;
    transition: .2s ease-out;
    text-align: left;
    line-height: 17px;
    font-family: var(--font_family_menu)
}

.nav .list>.first-level.sub>a .name:after,.nav-mobile .first-level>li.sub>a:after {
    content: "\ea01";
    font-family: go!important;
    font-style: normal!important;
    font-weight: 400!important;
    font-variant: normal!important;
    text-transform: none!important;
    height: 14px;
    font-size: .75rem;
    margin-left: .3125rem;
    vertical-align: middle;
    transition: .2s ease-out;
    display: none
}

.nav-mobile .first-level>li.sub>a:after {
    position: absolute;
    right: 15px
}

.nav .list .second-level {
    position: absolute;
    top: calc(100% - 55px);
    min-width: 275px;
    width: auto;
    padding: 25px;
    background-color: #fff;
    border-radius: 0 0 4px 4px;
    transform: translateY(55px);
    border-top: 3px solid var(--color_primary_medium);
    transition: opacity .2s ease-out 0;
    box-shadow: 0 4px 8px rgb(0 0 0/6%);
    scrollbar-color: var(--color_primary_medium);
    pointer-events: none
}

.banners-grid .item .item-image,.banners-grid .item img,.product,.product .image,.product .product-tags .tag,div#video-home video.video__home {
    border-radius: var(--border_radius_images)
}

.nav .first-level:hover .second-level {
    opacity: 1;
    visibility: visible;
    transform: translateY(55px);
    pointer-events: auto
}

.nav .list .second-level::-webkit-scrollbar {
    width: 6px
}

.nav .list .second-level::-webkit-scrollbar-track {
    background: #f5f5f5
}

.nav .list .second-level::-webkit-scrollbar-thumb {
    background: #d6d6d6
}

.fixed .nav .list>li>a .icon {
    max-height: 0;
    margin: 0;
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    transition: .2s ease-out .2s
}

@media(max-width: 1000px) {
    .nav .list>li>a {
        font-size:1rem;
        padding: 0 .625rem
    }

    #container-add-lista #add-listas {
        right: -1.25rem
    }
}

@media(max-width: 900px) {
    .nav .list>li>a {
        font-size:.875rem;
        padding: 0
    }
}

.menu-mobile,.nav-mobile .list li a {
    display: flex;
    color: var(--color_header_text)
}

.nav .third-level>li a {
    padding: .1rem 0;
    font-weight: 400
}

.nav>.container>.list>.sub.all-categories>.sub-list.second-level ul.wrapper-categories {
    width: 100%;
    -webkit-columns: 4;
    -moz-columns: 4;
    columns: 4
}

.menu-mobile {
    position: fixed;
    top: 0;
    left: 0;
    width: 85%;
    height: 100%;
    max-width: 500px;
    flex-direction: column;
    padding: 1rem 15px 0;
    background-color: var(--color_header_bg);
    transform: translateX(calc(-100% - 10px));
    box-shadow: 0 3px 12px rgba(0,0,0,.16);
    z-index: 20;
    transition: .3s ease-out
}

.cart.cart-show section#cart,.menu-mobile.show {
    transform: translateX(0)
}

.menu-mobile .close-box {
    top: 1.2rem;
    right: 6px;
    color: var(--color_header_text)
}

.menu-mobile .header-menu {
    margin-right: 1.25rem;
    cursor: default
}

.menu-mobile .block-title {
    width: 100%;
    font-size: 1.125rem;
    font-weight: 700;
    padding-right: 3.125rem;
    color: var(--color_header_text)
}

.menu-mobile .nav-mobile-wrapper {
    flex-grow: 1;
    height: calc(100% - 180px);
    margin: 1rem 0 0;
    padding: 1rem 0 .275rem;
    overflow: auto
}

.nav-mobile {
    height: 100%;
    overflow-x: hidden;
    scrollbar-color: var(--color_header_highlight) var(--color_header_details);
    padding-right: 15px
}

.nav-mobile::-webkit-scrollbar {
    width: 6px
}

.nav-mobile::-webkit-scrollbar,.nav-mobile::-webkit-scrollbar-track {
    background: var(--color_header_details)
}

.nav-mobile::-webkit-scrollbar-thumb {
    background: var(--color_header_highlight);
    border-radius: 5px
}

.nav-mobile .list li a {
    position: relative;
    align-items: center;
    padding: 5px 0
}

#tipos-listas li a,.banner-home .item a,.banner-line a,.banners-grid a,.email-texto a,.footer .box.box-infos li.hour span,.google-seal .icon,.is-custom-page .page-title .text,.nav-mobile li.sub.show>.second-level,.page-contact .block,.product-tabs .tabs-content .payment-tab .item-parcela,.product-tabs .tabs-content .tab.active,.product-wrapper .product-form .product-price-tray #info_preco br:not(:first-of-type) {
    display: block
}

.nav-mobile li.sub.show>a:after {
    transform: rotate(-180deg)
}

.item-image {
    position: relative;
    z-index: 1;
    overflow: hidden;
    display: block
}

.item-image:after {
    content: "";
    display: block;
    padding-bottom: var(--padding)
}

.item-image img {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
    transition: opacity .2s ease-out
}

.banner-home .item img {
    display: block;
    width: 100%
}

.banner-home .swiper-container .dots {
    margin: 0;
    position: absolute;
    bottom: 28px;
    z-index: 5
}

.banners-grid {
    margin: 1.875rem 0 0
}

.banners-grid .item {
    width: 100%;
    transition: .3s
}

.banners-grid.two .item {
    width: calc(50% - 15px)
}

.banners-grid .item a {
    width: 100%;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center
}

.banners-grid .item img {
    transition: .3s;
    max-width: 100%
}

.banners-grid img:hover {
    opacity: .9;
    transform: scale(1.02)
}

.banner-line {
    margin: 2.5rem 0;
    display: none
}

.banner-line img {
    width: 100%;
    display: block;
    transition: .3s ease-out;
    border-radius: 2px
}

.banner-line a:hover img {
    transform: scale(1.03)
}

.product-rating {
    font-size: 1rem;
    margin: .75rem 0;
    cursor: pointer
}

.product-rating .total {
    font-size: .75rem;
    font-weight: 500;
    margin-top: .3125rem;
    transition: .2s ease-out
}

.product-form .product-rating {
    flex-wrap: wrap
}

.product-form .product-rating .total {
    width: 100%;
    text-align: left;
    text-decoration: underline
}

.product-rating .icon {
    background: url(../img/empty-star.svg) 0 0/100% no-repeat;
    width: .938em;
    height: .938em;
    display: block
}

#coments .ranking .icon.active,.product-rating .icon.active,.rateBlock .starn.star-on {
    background-image: url(../img/star.svg)
}

.product-rating .icon:not(:last-child) {
    margin-right: .3125rem
}

.product-tags .tag {
    width: 53px;
    height: 40px;
    font-size: .75rem;
    font-weight: 700;
    line-height: 1.4;
    text-align: center;
    text-transform: uppercase;
    overflow: hidden
}

.product-tags .tag img {
    width: auto;
    height: auto;
    max-width: 100%
}

.footer .footer-main .newsletter .form .news-button .icon,.product-tags .tag+.tag {
    margin-left: 5px
}

.product {
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
    color: var(--color_font_medium);
    background-color: #fff;
    z-index: 3;
    transition: .2s ease-in-out;
    margin-top: 0;
    padding: 0 0 1px
}

.product .space-image {
    display: block;
    position: relative;
    overflow: hidden
}

.compreJunto .produto span>a:before,.compreJunto .produto span>div:before,.product .space-image:after {
    content: "";
    display: block;
    padding-bottom: var(--height_products,100%)
}

.product .space-image img {
    display: block;
    max-width: 100%;
    max-height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    object-fit: contain;
    object-position: center;
    width: 100%;
    transition: .3s
}

.product .product-tags {
    position: absolute;
    left: 7px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    display: flex;
    flex-direction: column;
    top: 5px
}

.product .product-tags[data-tags-count="3"],.product .product-tags[data-tags-count="4"] {
    bottom: -25px
}

.product .product-tags .tag {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    margin: 6px 0;
    color: var(--color_font_inverted);
    font-size: 11px;
    width: 100%;
    min-width: 100px;
    max-width: 100px;
    max-height: 100px
}

.page-navegacao_visitados .board h1.color,.product .product-tags .tag+.tag {
    margin: 0
}

.product .product-tags .tag:nth-child(n+3),.product .product-tags[data-tags-count="3"] .tag:nth-child(n+2) {
    margin-top: 4px
}

.product .product-tags-circle {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column
}

.product .product-tags-circle .tag-circle {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    border-radius: 3rem;
    width: 40px
}

.product .product-tags-circle .tag-circle+.tag-circle {
    margin-top: .25rem
}

#depoimento,#texto-termo,.lista_presentes table,.msg-aceita-termo,.page-listas_criar .board,.product .product-tags-circle .tag-circle.free-shipping,.product-wrapper .product-form .product-gifts h3#nomeBrinde {
    font-size: .875rem
}

.product .product-tags-circle .tag-circle.discount,.product .product-tags-circle .tag-circle.free-shipping,.product .product-tags-circle .tag-circle.progressive-discount {
    background-color: var(--color_primary_medium);
    color: var(--color_font_inverted)
}

.product .product-tags-circle .tag-circle.discount {
    flex-direction: column;
    font-size: .75rem;
    line-height: 1
}

.product .product-tags-circle .tag-circle.discount .discount-value {
    font-weight: 700;
    text-align: center;
    font-size: 11px
}

@media(max-width: 440px) {
    .product .product-tags-circle .tag-circle {
        width:34px;
        height: 34px
    }
}

.product .product-info {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: 15px 10px 6px;
    text-align: center
}

.product .product-info .wrapper-product-name {
    height: 40px
}

.product .product-info .product-name {
    color: var(--color_font_dark);
    flex-grow: 1;
    font-size: 16px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-weight: 400;
    line-height: normal
}

.box-noticia,.product .product-price {
    flex-direction: column;
    transition: .2s ease-out
}

.product .product-rating {
    height: 15px;
    overflow: hidden;
    transition: .2s ease-out
}

.product .product-price {
    display: flex;
    justify-content: center;
    flex-grow: 1;
    min-height: 50px;
    padding-top: 0
}

.product .product-price .product-has-variants {
    font-size: 14px;
    margin-bottom: -2px
}

.product .product-price .price .oculta_destaque,.product .product-price .price .old-price,.product .product-price .price .precode {
    font-size: 13px;
    text-decoration: line-through
}

.product .product-price .price.display-cash {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center
}

.product .product-price .price.display-cash span.old-price {
    margin-right: 8px
}

.product .product-price .price .current-price {
    display: block;
    font-size: 17px;
    font-weight: 700;
    color: var(--color_font_price)
}

.product .product-price .product-installments {
    font-size: .8rem;
    font-weight: 400;
    display: block;
    width: 100%;
    margin-top: 0;
    line-height: normal
}

.product .price .product-installments strong {
    color: var(--color_font_price)
}

.product .product-price .product-message {
    font-size: 19px;
    font-weight: 400;
    color: var(--color_font_dark)
}

.product .product-price .product-message .notify-me {
    display: block;
    font-size: .875rem;
    font-weight: 500;
    margin-top: .3125rem;
    color: var(--color_font_dark)
}

.product .product-price .product-message .notify-me .icon {
    font-size: .5rem
}

.product .actions {
    padding: 0 15px
}

@media(max-width: 870px) {
    .product .tags {
        display:none
    }
}

@media(max-width: 450px) {
    .product .product-button .icon {
        font-size:1.25rem;
        height: 22px
    }

    .product .quantity-buttons {
        display: none
    }
}

.section-showcase {
    position: relative;
    padding: .5rem 0 0
}

.section-showcase.z-index {
    z-index: 5
}

.list-product {
    padding: 0 5px
}

.list-product .item {
    display: flex;
    height: auto;
    width: 25%;
    margin-bottom: 1.5rem;
    box-sizing: border-box;
    padding: 10px
}

@media(max-width: 1080px) {
    .list-product:not(.swiper-wrapper) .item {
        width:33.33333%
    }
}

.box-noticia {
    display: flex;
    flex-grow: 1;
    width: 100%;
    height: 100%;
    align-items: center;
    border: 1.5px solid var(--color_gray_medium);
    border-radius: var(--border_radius_images);
    overflow: hidden
}

.box-noticia:hover {
    box-shadow: 0 4px 8px var(--color_gray_medium)
}

.box-noticia #noticia_imagem {
    width: 100%;
    overflow: hidden;
    position: relative
}

.box-noticia #noticia_imagem a {
    display: block;
    width: 100%;
    height: 100%;
    cursor: pointer
}

.box-noticia #noticia_dados h3 a,.box-noticia #noticia_dados p,.dep_dados .dep_msg {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    white-space: normal
}

.section-notices .noticias>li {
    width: calc(100% / 3)
}

.box-noticia #noticia_imagem>a:after {
    display: block;
    content: "";
    padding-bottom: 100%
}

.box-noticia #noticia_imagem>a img {
    position: absolute;
    left: 0;
    top: 0
}

.box-noticia #noticia_imagem img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: .2s ease-out
}

.box-noticia #noticia_dados {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    width: 100%;
    padding: 15px 20px;
    position: relative;
    text-align: left
}

.box-noticia #noticia_dados h3 {
    font-size: 0;
    font-weight: 700;
    margin-top: 5px;
    flex-grow: 1
}

.box-noticia #noticia_dados h3 a {
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 0;
    color: var(--color_font_medium);
    line-height: 1.15;
    -webkit-line-clamp: 2;
    transition: .2s ease-out;
    text-align: left
}

.box-noticia #noticia_dados p {
    max-height: 58px;
    font-size: .875rem;
    line-height: 1.36;
    flex-grow: 1;
    color: var(--color_font_medium);
    -webkit-line-clamp: 3
}

.box-noticia #noticia_dados .button-show {
    background: var(--color_primary_medium);
    color: var(--color_font_inverted);
    display: block;
    margin: 20px 0 0;
    width: 100%;
    max-width: 196px;
    height: 41px;
    border-radius: 2px;
    line-height: 41px;
    text-align: center;
    font-size: .87rem;
    font-weight: 700;
    text-transform: uppercase;
    transition: .2s ease-out
}

.box-noticia:hover #noticia_imagem img {
    transform: scale(1.1)
}

.dep_item {
    display: flex;
    padding: 0 15px;
    width: 33.333333%
}

.dep_dados,.editDep>li {
    padding: 1.5625rem 2.5rem
}

.dep_item.swiper-slide {
    height: auto
}

.dep_item a {
    position: relative;
    background-color: var(--color_gray_medium);
    border-radius: 8px
}

.dep_dados,.dep_item a {
    display: flex;
    flex-grow: 1
}

.dep_dados {
    flex-direction: column;
    text-align: center;
    z-index: 0;
    background: #fff;
    border: 1px solid var(--color_gray_medium);
    border-radius: var(--border_radius_images)
}

.dep_dados .dep_img {
    position: absolute;
    top: 33px;
    left: 20px;
    bottom: 0;
    z-index: -1;
    opacity: .7
}

.dep_dados .dep_msg {
    flex-grow: 1;
    font-weight: 400;
    order: -2;
    -webkit-line-clamp: 3;
    line-height: 1.25;
    text-align: center;
    transition: .3s ease-in-out
}

.dep_dados .dep_nome {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5;
    margin: .625rem 0 0
}

.section-avaliacoes ul.dep_dados:before {
    content: "";
    width: 100px;
    background: url(http://images.tcdn.com.br/files/1160525/themes/3/img/five_star.png?205.00523335883358) center center no-repeat;
    height: 30px;
    margin: 0 auto 7px;
    order: -10
}

.page-busca_noticias .page-content,.page-depoimentos .page-content,.section-avaliacoes {
    opacity: 0;
    visibility: hidden;
    transition: .4s ease-out
}

.section-avaliacoes .swiper-container .next,.section-avaliacoes .swiper-container .prev {
    top: 38%;
    bottom: auto
}

.breadcrumb {
    width: 100%;
    margin: 0 0 15px;
    font-size: .75rem;
    justify-content: center;
    text-align: center
}

.breadcrumb a {
    font-weight: 400;
    transition: color .3s ease-out
}

.breadcrumb .breadcrumb-item:not(:last-child) {
    padding-right: 25px;
    position: relative
}

.breadcrumb .breadcrumb-item:not(:last-child):after {
    content: "/";
    display: block;
    position: absolute;
    top: 0;
    right: 10px
}

.pagination .page {
    margin: 0 .1875rem
}

.pagination .page:last-child {
    margin-right: 0
}

.pagination .page a,.pagination .page.current {
    display: block;
    min-width: 44px;
    height: 38px;
    font-size: 1rem;
    font-weight: 600;
    line-height: 38px;
    text-align: center;
    background-color: var(--color_gray_medium);
    color: var(--color_primary_medium);
    border-radius: 2px;
    transition: .2s ease-out
}

.pagination .page.first a,.pagination .page.last a {
    padding: 0 1.25rem
}

.btns-paginator.selectedPg,.btns-paginator:hover,.pagination .page.current {
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    font-size: .875rem;
    font-weight: 700;
    text-align: center;
    line-height: 38px
}

.pagination .page .icon {
    font-size: .625rem
}

.catalog-cols .sidebar-category {
    width: 285px;
    padding-right: 2.5rem
}

.catalog-cols .col-content .catalog-empty,.page-register h2 {
    font-size: 1rem;
    text-align: center
}

.catalog-header .catalog-info {
    margin-bottom: 2.5rem
}

.catalog-header .catalog-info .catalog-name {
    position: relative;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--color_font_dark);
    max-width: 365px;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
    display: -webkit-box
}

.catalog-header .catalog-info .catalog-name .search-results {
    font-size: 12px;
    font-weight: 400;
    display: block;
    color: var(--color_primary_medium)
}

#depoimento #aviso_depoimento>div,.caixa-cadastro fieldset .botao-novo-cadastro,.catalog-header .banner+.catalog-info {
    margin-top: 1.875rem
}

.catalog-header .banner img {
    display: block;
    width: 100%;
    height: 100%;
    max-height: 250px;
    object-fit: cover;
    transition: .2s ease-out
}

#produto_comprar a:hover:before,.catalog-header .banner a:hover img {
    opacity: .9
}

.catalog-header .catalog-info .catalogo-form-filtros,.filters-list,.product-wrapper .product-form .tray-kit .cor_variacao li>div,.products-history .ValoresLista {
    font-size: .75rem
}

.catalog-header .catalog-info .catalogo-form-filtros select.select {
    height: 50px;
    min-width: 207px;
    padding-left: .875rem;
    font-size: 1rem;
    font-weight: 600
}

.catalog-header .description {
    margin: -.9375rem 0 2.5rem
}

.catalog-content .showcase-catalog .list-product {
    margin: 0 -10px
}

@media(min-width: 768px) {
    .modal .modal-dialog {
        width:600px;
        margin: 30px auto
    }

    .modal .modal-content {
        box-shadow: 0 5px 15px rgba(0,0,0,.5)
    }

    .modal .modal-sm {
        width: 300px
    }

    .button-filter,.catalog-cols .sidebar-category .sidebar-mobile-header,.catalog-header .catalog-info .button-filter-mobile,.catalog-header .catalog-info .sort-mobile,.header-menu,.pagination .page.first .icons,.pagination .page.last .icons,.product .product-tags-circle .tag-circle.free-shipping,.swiper-container.hide-dots-desk .dots {
        display: none
    }

    .header {
        position: sticky;
        width: 100%;
        z-index: 10;
        top: 0;
        left: 0
    }

    .header.fixed {
        box-shadow: 0 0 8px -2px rgb(0 0 0 / 24%);
        border: 0!important
    }

    .header .logo {
        margin: 0 auto;
        width: auto
    }

    .header .logo img,.header .logo svg {
        transition: .3s;
        margin: 20px 0;
        max-height: var(--height_logo_desktop);
        max-width: 100%;
        aspect-ratio: auto
    }

    .header.fixed .logo img,.header.fixed .logo svg {
        transition: .3s;
        margin: 10px 0;
        max-height: calc(var(--height_logo_desktop) - 5px)
    }

    .banner-line.desk,.pagination .page.first .text,.pagination .page.last .text {
        display: block
    }

    .product.show-down:hover .product-rating {
        opacity: 0
    }

    .product.show-down:hover .product-rating+.product-price {
        transform: translateY(-25px)
    }

    .product:hover .actions {
        opacity: 1;
        visibility: visible;
        transition-delay: .2s
    }

    .catalog-header .catalogo-form-filtros label:not(:first-child):before {
        content: "Ordenar por:";
        font-size: .875rem;
        font-weight: 500;
        padding-right: 1.25rem
    }

    .catalog-cols .sidebar-category+.col-content {
        width: calc(100% - 285px)
    }

    .catalog-cols .sidebar-category+.col-content .showcase-catalog .list-product .item {
        width: 33.333333%
    }

    .footer .payment-list,.footer .security-seals {
        padding-top: 5px;
        justify-content: flex-start
    }

    header.header.header-center .header-desktop .line .search-move {
        order: -1;
        padding: 0;
        flex: 0 0 auto;
        width: 400px
    }

    header.header.header-center .header-desktop .header-right {
        margin: 0;
        width: 400px;
        justify-content: flex-end;
        max-width: 100%;
        padding-right: 15px
    }

    header.header.header-center .header-desktop .line .search-move .header-search-wrapper {
        margin: 0 auto 0 0;
        max-width: 90%
    }
}

@media(min-width: 768px) and (max-width:960px) {
    .catalog-cols .sidebar-category+.col-content .showcase-catalog .list-product .item {
        width:50%
    }
}

.smart-filter {
    padding: .5rem .9375rem;
    border: 1px solid var(--color_gray_dark);
    border-radius: 4px
}

#tipos-listas li,.page-listas_busca table.busca,.page-newsletter .formulario-newsletter p:first-child,.progressive-discount-banners,.smart-filter .filter-block {
    margin-bottom: 1.875rem
}

.smart-filter .filter-block:last-child ul {
    border-bottom: 0;
    margin-bottom: 0;
    padding-bottom: 0
}

.smart-filter .filter-title {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color_font_dark);
    padding: .625rem 0;
    cursor: pointer;
    border-bottom: transparent;
    transition: .1s ease-out .3s
}

.smart-filter .filter-button,.tag-discount {
    text-align: center;
    color: var(--color_font_inverted)
}

.smart-filter .filter-block.closed .filter-title,.smart-filter .filter-list {
    border-bottom: 1px solid var(--color_gray_dark)
}

.smart-filter .filter-list {
    margin-bottom: 1.875rem;
    padding-bottom: 1.375rem;
    transition: border-bottom .1s ease-out .2s
}

.smart-filter .filter-block.closed .filter-list {
    border-bottom: transparent;
    transition: border-bottom .2s ease-out
}

.smart-filter .filter-list .filter-item {
    padding: .5rem 0
}

.checkbox-accept-terms,.product-wrapper .product-tabs .tabs-nav .tab-link .text,.smart-filter .filter-checkbox,.template-instagram div#instafeed a {
    position: relative;
    display: block
}

.checkbox-accept-terms input,.smart-filter .filter-checkbox .filter-input {
    position: absolute;
    left: 0;
    z-index: -1;
    width: 1rem;
    height: 1.25rem;
    opacity: 0
}

.smart-filter .filter-checkbox .filter-label {
    position: relative;
    display: block;
    padding-left: 1.75rem;
    cursor: pointer
}

.smart-filter .filter-checkbox .filter-name {
    font-size: .875rem;
    font-weight: 500;
    vertical-align: top
}

.smart-filter .filter-checkbox .filter-count {
    font-size: .75rem;
    vertical-align: text-top
}

.smart-filter .filter-checkbox .filter-label:before {
    position: absolute;
    content: "";
    top: .125rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    background-color: #fff;
    border: 1px solid var(--color_gray_dark);
    border-radius: 2px;
    transition: .2s ease-in-out;
    box-sizing: border-box
}

.smart-filter .filter-checkbox .filter-label:after {
    position: absolute;
    content: "";
    top: .125rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    background: 50%/50% 50% no-repeat
}

.smart-filter .filter-checkbox .filter-input:checked~.filter-label:before {
    color: var(--color_font_medium);
    border-color: var(--color_primary_medium);
    background-color: var(--color_primary_medium)
}

.checkbox-accept-terms input:checked~label:after,.smart-filter .filter-checkbox .filter-input:checked~.filter-label:after {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvgxmlns='http://www.w3.org/2000/svg'width='8'height='8'%3E%3Cpathfill='%23fff'd='M6.564.75l-3.593.612-1.538-1.55L04.26l2.9742.99L82.193z'/%3E%3C/svg%3E")
}

.smart-filter .filter-button {
    width: 100%;
    margin: 0;
    padding: 10px;
    font-size: .875rem;
    font-weight: 700;
    text-transform: uppercase;
    background-color: var(--color_secondary_medium);
    transition: .2s ease-out;
    font-family: var(--font_family);
    border-radius: var(--border_radius_buttons)
}

#tipos-listas img,.progressive-discount-banners img {
    max-width: 100%;
    margin: 0 auto;
    display: block
}

.product-wrapper {
    margin-bottom: 5rem
}

.product-wrapper .product-box {
    display: flex;
    align-items: flex-start
}

.product-wrapper .product-box .product-video {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    padding: .3125rem 0;
    color: red;
    border-radius: 2px;
    cursor: pointer;
    z-index: 5;
    transition: .2s ease-out
}

.product-wrapper .product-box .product-video .text {
    width: 0;
    margin-left: .3125rem;
    font-size: .875rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    transition: .2s ease-out
}

.product-wrapper .product-box .product-video .icon {
    font-size: 30px;
    height: 30px
}

.product-tabs .tabs-content .payment-tab .option-details table td:last-child,.product-tabs .tabs-content .payment-tab .option-details table th:last-child,.product-wrapper .product-box .product-video .icon:before {
    vertical-align: top
}

.product-wrapper .product-box .product-video .icon:after {
    content: "";
    background: #fff;
    position: absolute;
    width: 12px;
    height: 12px;
    top: 14px;
    left: 9px;
    z-index: -1
}

.product-wrapper .product-box .product-video:hover .text {
    opacity: 1;
    width: 70px
}

.product-wrapper .product-box .product-gallery {
    width: calc(90% - 470px);
    padding-right: 2.5rem;
    position: relative;
    display: flex
}

.tag-discount {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 50%;
    font-size: .75rem;
    line-height: 1;
    position: absolute;
    top: .25rem;
    right: 1.25rem;
    z-index: 2;
    font-weight: 700;
    background-color: var(--color_primary_medium)
}

.product-wrapper .product-gallery .product-images {
    width: calc(80% - 90px);
    height: 100%;
    max-height: initial;
    margin: 0 auto
}

.product-wrapper .product-gallery .product-images .image {
    cursor: move
}

.product-wrapper .product-gallery .product-images .zoom {
    width: 100%;
    height: 100%;
    position: relative
}

.product-wrapper .product-gallery .product-images .zoom:before {
    content: "";
    display: block;
    padding-bottom: var(--height_products_single,100%)
}

.product-wrapper .product-gallery .product-images img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain
}

.product-wrapper .product-gallery .product-thumbs {
    opacity: 0;
    visibility: hidden;
    transition: .3s ease-out;
    height: 450px;
    width: 90px;
    order: -1
}

.product-wrapper .product-gallery .product-thumbs .swiper-slide {
    padding: 4px
}

.product-wrapper .product-gallery .product-thumbs .swiper-slide .thumb {
    border: 1px solid #e7e7e7;
    cursor: pointer;
    border-radius: 5px;
    opacity: .7;
    transition: .2s ease-out
}

.product-wrapper .product-gallery .product-thumbs .swiper-slide .thumb:hover,.product-wrapper .product-gallery .product-thumbs .swiper-slide.swiper-slide-thumb-active .thumb {
    opacity: 1;
    border: 1px solid var(--color_primary_medium)
}

.product-wrapper .product-gallery .product-thumbs .swiper-slide img {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    height: 100%;
    object-fit: contain
}

.email-texto a,.footer .copy .tray .mode-preview,.info-form a,.product-wrapper .product-gallery .product-thumbs.show-arrows .next,.product-wrapper .product-gallery .product-thumbs.show-arrows .prev {
    transition: .2s ease-out
}

.product-wrapper .product-gallery .product-thumbs.show-arrows .next.swiper-button-disabled,.product-wrapper .product-gallery .product-thumbs.show-arrows .prev.swiper-button-disabled {
    visibility: visible;
    opacity: .7;
    cursor: default
}

.product-wrapper .product-box .product-form {
    position: relative;
    width: 520px;
    padding: 0 0 0 30px;
    border-radius: 6px
}

.product-wrapper .product-form #opcoes0 ul,.product-wrapper .product-form .cor_variacao ul,.product-wrapper .product-form .product-tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start
}

.product-wrapper .product-form .product-tags .tag {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30%;
    height: 30px;
    margin: 0 7px 7px 0;
    border-radius: var(--border_radius_buttons);
    color: var(--color_font_inverted)
}

.product-wrapper .product-box .product-additional-fields .varCont,.product-wrapper .product-form .product-tags+.product-name {
    margin-top: 5px
}

header .contact .contact-text strong {
    font-size: 14px;
    display: block;
    font-weight: 700;
    margin-top: -1px
}

.product-wrapper .product-form .product-name {
    font-size: 22px;
    font-weight: 700;
    color: var(--color_font_dark);
    text-align: left;
    line-height: normal
}

.product-wrapper .product-form .product-main-info {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
    margin-top: .3125rem
}

.product-wrapper .product-form .product-brand,.product-wrapper .product-form .product-sku {
    margin: 0 5px 0 0;
    font-size: .75rem;
    font-weight: 500;
    text-transform: uppercase
}

.product-wrapper .product-form .product-brand+.wishlist,.product-wrapper .product-form .product-sku+.wishlist,.product-wrapper .product-form .product-social-share .addthis_counter.addthis_pill_style.at_native_button,.rateBlock .nota {
    margin-left: .625rem
}

.product-wrapper .product-form .product-rating {
    justify-content: flex-start;
    margin: 10px 0
}

.product-wrapper .product-form .product-info {
    margin-top: 1.25rem;
    font-size: .75rem
}

#coments #form-comments label h3,.banners-grid,.product-wrapper .product-form .product-info+.product-info,.product-wrapper .product-tabs.short-tabs {
    margin-top: 0
}

#acceptTerm,.product-wrapper .product-form .actions,.product-wrapper .product-form .product-name+.product-additional-message,.product-wrapper .product-form .variant-error {
    margin-top: 1.25rem
}

.product-wrapper .product-form .product-info .bold,.products-history .ValoresLista strong {
    font-weight: 500
}

.product-wrapper .product-form .product-additional-message {
    font-size: .875rem;
    margin-bottom: 1.25rem;
    text-align: left
}

.product-wrapper .product-form .product-variants {
    margin: 1.25rem 0 1.5rem
}

.product-wrapper .product-form .texto_variacao {
    font-size: .875rem;
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--color_font_dark);
    text-align: left;
    margin-top: 15px
}

.product-wrapper .product-form .texto_variacao h2 {
    font-weight: inherit;
    display: inline;
    font-size: inherit
}

.product-wrapper .product-form .texto_variacao h2:after {
    content: ":"
}

#container-add-lista .listas a:hover,#container-add-lista .listas a:hover:before,#listaCategoriasNoticias li a:hover,.filters-list a:hover,.info-form a:hover,.product-tabs.short-tabs .tabs-content .tab-link-mobile.active,.product-wrapper .product-form .texto_variacao span,.product-wrapper .product-form .tray-kit .texto_variacao h2,.sidebar-central a:hover {
    color: var(--color_primary_medium)
}

.product-wrapper .product-form #opcoes0 ul:not(.listaVarMultipla),.product-wrapper .product-form .cor_variacao ul:not(.listaVarMultipla) {
    margin-left: -5px
}

.product-wrapper .product-form #opcoes0 ul {
    flex-direction: column
}

.product-wrapper .product-form .cor_variacao ul li {
    margin: 0 .3125rem 5px;
    position: relative
}

.product-wrapper .product-form .cor_variacao li img {
    display: block;
    border: 2px solid var(--color_gray_dark);
    box-shadow: 0 0 0 var(--color_primary_medium);
    transition: .2s ease-out;
    border-radius: 50px;
    max-height: 34px;
    max-width: 34px;
    width: 34px;
    height: 34px
}

.product-wrapper .product-form .cor_variacao li img.cor_selecionada,.product-wrapper .product-form .cor_variacao li img:hover {
    box-shadow: 0 0 0 2px var(--color_secondary_medium);
    border-color: #fff
}

.product-wrapper .product-form .cor_variacao select {
    width: 100%;
    display: block;
    padding-left: 18px;
    font-size: .75rem;
    max-width: 350px
}

.caixa-login fieldset,.product-wrapper .product-form .cor_variacao.passo2 {
    margin-top: 10px
}

.product-wrapper .product-form .cor_variacao li.sem_estoque:after {
    content: "x";
    color: red;
    display: block;
    position: absolute;
    right: -3px;
    bottom: -6px;
    font-size: 18px
}

.product-wrapper .product-form .cor_variacao li.sem_estoque img.cor_selecionada {
    box-shadow: 0 0 0 2px rgba(0,0,0,.5)
}

.product-wrapper .product-form .cor_variacao li>div {
    min-width: 42px;
    height: 42px;
    padding: 0 .625rem;
    text-align: center;
    font-size: .875rem;
    font-weight: 500;
    line-height: 40px;
    color: var(--color_primary);
    border: 1px solid var(--color_gray_dark);
    transition: .3s ease-in-out;
    border-radius: var(--border_radius_images)
}

.product-wrapper .product-form .cor_variacao li.sem_estoque>div,.product-wrapper .product-form .cor_variacao li.sem_estoque>div.cor_selecionada {
    background: #e4e4e4!important;
    border-color: #e4e4e4;
    color: #707070
}

.product-wrapper .product-form .cor_variacao li>div.cor_selecionada {
    background-color: var(--color_secondary_medium);
    border-color: var(--color_secondary_medium);
    color: var(--color_font_inverted)
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row!important;
    margin: 0 -5px
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) li {
    padding: 0 5px 5px;
    margin: 0
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label input {
    position: absolute;
    z-index: -1;
    opacity: .001;
    width: 0;
    height: 0
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label span {
    display: block;
    min-width: 42px;
    height: 42px;
    line-height: 40px;
    text-align: center;
    font-size: .875rem;
    font-weight: 600;
    border: 1px solid var(--color_gray_dark);
    transition: .3s ease-in-out;
    cursor: pointer;
    padding: 0 8px;
    border-radius: 3px
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label span:hover {
    background-color: #efefef
}

.product-wrapper .product-form .lista_radios:not(.listaVarMultipla) label input:checked+span {
    background-color: var(--color_primary_medium);
    border-color: var(--color_primary_medium);
    color: var(--color_gray_dark)
}

#NavLogoTray .logotray-message,#coments .ranking .rating,#form_comprar .lista_radios.listaVarMultipla .labelMultiVariacao,#form_comprar .lista_radios.listaVarMultipla .labelQuantVariacao,#form_comprar .lista_radios.listaVarMultipla>li,.quick-buy .quick-buy__content,.rateBlock .stars,header .contact.gift>a {
    display: flex;
    align-items: center
}

#form_comprar .labelMultiVariacao,#form_comprar .labelQuantVariacao {
    display: inline-block;
    vertical-align: top;
    float: none;
    font-size: .75rem;
    font-weight: 500
}

.central-menu,.compreJunto .produto,.compreJunto form,.compreJunto form .fotosCompreJunto,.editComment li {
    display: -webkit-box;
    display: -ms-flexbox
}

#form_comprar .lista_radios.listaVarMultipla>li>span {
    font-size: .87rem;
    font-weight: 600;
    margin: 15px 0 8px
}

#form_comprar .lista_radios.listaVarMultipla>li {
    flex-wrap: wrap;
    margin-bottom: 15px
}

#form_comprar .labelMultiVariacao {
    width: 63%
}

.product-wrapper .product-form .labelMultiVariacao .multivar {
    margin-right: 5px;
    cursor: pointer
}

#form_comprar .labelQuantVariacao {
    text-align: center;
    margin-left: 5px;
    margin-right: 0
}

#form_comprar .labelQuantVariacao .inputQuantVariacao {
    min-width: auto!important;
    margin: 0!important;
    width: 35px;
    padding: 0!important;
    text-align: center;
    height: 35px
}

.product-wrapper .product-form .listaVarMultipla img {
    border: 1px solid var(--color_gray_dark);
    border-radius: 3px;
    margin-right: 5px
}

#form_comprar .lista_radios.listaVarMultipla .currency {
    margin-left: 5px;
    text-decoration: none
}

.product-wrapper .product-form .product-gifts {
    margin: 20px 0 10px;
    text-align: left
}

.product-wrapper .product-form .product-price-tray #produto_preco span.color-tone-2:first-child {
    font-size: .75rem;
    font-weight: 500;
    text-transform: lowercase
}

.product-wrapper .product-form .product-price-tray #produto_preco .currency,.quick-buy #produto_preco .currency {
    text-decoration: none;
    text-transform: uppercase
}

.product-wrapper .product-form .product-price-tray #produto_preco {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    flex-direction: column;
    align-items: flex-start
}

.product-wrapper .product-form .product-price-tray #produto_preco #precoDe {
    font-size: .875rem;
    font-weight: 500;
    text-decoration: line-through;
    text-transform: lowercase;
    color: var(--color_primary_medium)
}

.product-wrapper .product-form .product-price-tray #produto_preco .PrecoPrincipal {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 3px;
    color: var(--color_font_price)
}

.product-wrapper .product-form .product-price-tray #produto_preco #info_preco {
    font-size: .875rem;
    font-weight: 400;
    text-align: initial;
    line-height: normal;
    width: 100%
}

.product-wrapper .product-form .product-price-tray #produto_preco #info_preco strong,.quick-buy #produto_preco #info_preco strong {
    font-weight: 700;
    color: var(--color_font_price)
}

.product-wrapper .product-form .product-batch-promotion {
    margin: 1.25rem 0
}

.product-wrapper .product-form .product-batch-promotion img {
    display: block;
    width: 100%;
    max-width: 100px
}

.product-wrapper .product-form .product-batch-promotion.text-promotion {
    padding: 8px;
    border: 1px solid var(--color_gray_dark);
    font-size: 12px;
    font-weight: 500;
    text-align: center
}

.product-wrapper .product-form .product-progressive-discount {
    margin: 1.25rem 0;
    padding: .9375rem 1.25rem;
    font-size: .75rem;
    font-weight: 500;
    border-radius: 2px;
    background: #f9f9f9;
    border: 1px solid #f2f2f2
}

.product-wrapper .product-form .product-progressive-discount .title {
    display: block;
    margin-bottom: 8px;
    font-size: .875rem;
    text-align: left;
    color: var(--color_font_dark);
    font-weight: 700
}

.product-wrapper .product-form .product-progressive-discount .discounts .discount {
    padding: .1875rem 0
}

.product-wrapper .product-form .product-progressive-discount .tooltip {
    display: inline-block;
    position: relative;
    vertical-align: middle;
    margin-left: 5px
}

.product-wrapper .product-form .product-progressive-discount .tooltip .icon {
    color: var(--color_primary_medium);
    cursor: pointer
}

.product-wrapper .product-form .product-progressive-discount .tooltip-content {
    position: absolute;
    width: 200px;
    left: 50%;
    padding: .625rem;
    font-size: 12px;
    font-weight: 400;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,.11);
    transform: translateX(-50%);
    opacity: 0;
    visibility: hidden;
    transition: .2s ease-out
}

.product-wrapper .product-form .product-progressive-discount .tooltip-content:before {
    content: "";
    position: absolute;
    top: -4px;
    left: 50%;
    width: 8px;
    height: 4px;
    background-color: #fff;
    clip-path: polygon(50% 0,0 100%,100% 100%);
    transform: translateX(-50%)
}

.product-wrapper .product-form .product-reward {
    margin: 1.25rem 0;
    padding: .5rem;
    font-size: .75rem;
    font-weight: 500;
    text-align: center;
    border: 1px solid var(--color_gray_medium)
}

.product-wrapper .product-form .product-reward strong:last-child {
    font-weight: 500;
    margin-left: 2px
}

.page-listas_busca h2~ul,.product-wrapper .product-form .product-kit {
    margin: 1.875rem 0
}

.product-wrapper .product-form .tray-kit {
    font-size: .875rem;
    border: 1px solid var(--color_gray_dark);
    border-radius: 2px
}

.product-wrapper .product-form .tray-kit .tray-kit-title h3 {
    margin: .625rem 0;
    font-size: .875rem;
    font-weight: 600;
    color: var(--color_font_dark);
    text-align: center
}

.product-wrapper .product-form .tray-kit .tray-kit-item {
    padding: .625rem;
    border-top: 1px solid var(--color_gray_dark)
}

.product-wrapper .product-form .tray-kit .tray-kit-item>div {
    display: flex;
    position: relative
}

.product-wrapper .product-form .tray-kit .tray-kit-image {
    width: 70px;
    padding-right: 20px
}

.product-wrapper .product-form .tray-kit .tray-kit-image img {
    max-width: 100%;
    display: block;
    margin: auto
}

.product-wrapper .product-form .tray-kit .tray-kit-unity {
    font-size: .75rem;
    width: 60px;
    text-align: center
}

.product-wrapper .product-form .tray-kit .tray-kit-info {
    width: calc(100% - 150px)
}

.product-wrapper .product-form .tray-kit .tray-kit-info h3 {
    font-size: .875rem;
    font-weight: 500;
    margin-bottom: 10px;
    color: var(--color_font_dark)
}

.product-wrapper .product-form .tray-kit .cor_variacao li {
    margin: .3125rem
}

.product-wrapper #product-form-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%
}

.product-wrapper .product-form #quant {
    height: 50px;
    width: 100%;
    text-align: center;
    background: 0;
    border-color: var(--color_gray_dark)
}

.product-wrapper .product-form #quantidade {
    margin-right: 10px
}

.product-wrapper .product-form #quant,.product-wrapper .product-form #quantidade {
    font-size: .87rem;
    font-weight: 600;
    border-radius: var(--border_radius_buttons)
}

.product-wrapper .product-form #bt_comprar {
    max-width: 100%;
    width: 100%
}

.product-wrapper .product-form #button-buy {
    height: 50px;
    width: 100%;
    padding: 0;
    font-size: 17px;
    border-radius: var(--border_radius_buttons);
    background: var(--color_button_buy_bg);
    color: var(--color_button_buy_text)!important
}

.product-wrapper .product-form .product-price-tray #produto_nao_disp h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color_font_dark);
    display: block;
    line-height: 1.25
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp,.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp div#letmeknow_response,.product-wrapper .product-form .product-price-tray #produto_nao_disp #letmeknow_response {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp label,.product-wrapper .product-form .product-price-tray #produto_nao_disp #letmeknow_response label {
    width: calc(100% - 120px);
    max-width: unset
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #nao_disp #email_avise,.product-wrapper .product-form .product-price-tray #produto_nao_disp #letmeknow_response #email_avise {
    border-radius: 2px 0 0 2px
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #letMeKnow,.product-wrapper .product-form .product-price-tray #produto_nao_disp #letMeKnow {
    height: 52px
}

.product-wrapper .product-form .product-not-sale #produto_nao_disp #letMeKnow:before,.product-wrapper .product-form .product-price-tray #produto_nao_disp #letMeKnow:before {
    display: block;
    box-sizing: border-box;
    width: 120px;
    padding: 0;
    font-size: .75rem;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 0 2px 2px 0
}

.product-wrapper .product-form .product-not-sale .botao-sob-consulta {
    font-size: 0;
    line-height: 0
}

.product-wrapper .product-form .product-not-sale .botao-sob-consulta:before {
    content: "Produto sob consulta";
    display: block;
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
    color: var(--color_font_dark)
}

.product-wrapper .product-form .product-not-sale .botao-sob-consulta:after {
    content: "Preencha o nosso formul\00E1 rio de produtos sob consulta.";
    display: block;
    margin-top: -.9375rem;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.4
}

.product-wrapper .product-form .product-not-sale .botao_tire_duvidas {
    margin-top: 25px;
    font-size: 0;
    line-height: 0
}

.product-wrapper .product-form .product-not-sale .botao_tire_duvidas:before {
    content: "Preencher formul\00E1 rio";
    display: block;
    font-size: .875rem;
    font-weight: 600;
    margin-top: 1.875rem;
    text-align: center;
    line-height: 48px;
    text-transform: uppercase;
    border-radius: 2px;
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    transition: .2s ease-out;
    border-radius: var(--border_radius_buttons)
}

.product-wrapper .product-form .product-not-sale .botao-nao_indisponivel:before {
    content: "Produto indispon\00ED vel :(";
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color_font_dark);
    display: block;
    line-height: 1.25
}

.product-wrapper .product-form .product-not-sale .botao-nao_indisponivel:after {
    content: "Preencha seu e-mail que te avisaremos quando o produto estiver dispon\00ED vel para compra!";
    display: block;
    font-size: .875rem;
    margin-top: -.9375rem;
    font-weight: 400
}

.product-wrapper .product-form .product-shipping {
    margin-top: 24px;
    display: flex;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid var(--color_gray_dark);
    flex-wrap: wrap;
    padding-bottom: 10px
}

.product-wrapper .product-form .product-shipping .info {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 500;
    line-height: 17px;
    padding-right: 15px;
    width: 38%
}

.product-wrapper .product-form .product-shipping .info .icon {
    font-size: 1.75rem;
    margin-right: .625rem
}

.product-wrapper .product-form .product-shipping .shipping-form {
    position: relative;
    max-width: 62%;
    margin: 0 auto;
    width: 100%
}

.product-wrapper .product-form .product-shipping .input {
    width: 100%;
    height: 42px;
    padding: 0 4.25rem 0 1.2rem;
    color: var(--color_font_medium);
    border: 1px solid var(--color_gray_dark);
    background-color: transparent
}

.product-wrapper .product-form .product-shipping .submit-shipping {
    position: absolute;
    top: 0;
    right: 0;
    height: 42px;
    padding: 0 15px 0 10px;
    font-family: var(--font_family);
    font-size: .75rem;
    font-weight: 700;
    text-transform: uppercase;
    background-color: transparent;
    color: var(--color_primary_medium);
    transition: .2s ease-out
}

.product-wrapper .product-form .product-shipping .result.loading {
    position: relative;
    min-height: 90px;
    margin-top: 1.875rem
}

.product-wrapper .product-form .product-shipping .result {
    margin-top: 20px;
    width: 100%;
    flex: 0 0 100%
}

.product-wrapper .product-form .product-shipping .shipping-rates-table {
    font-size: .875rem;
    width: 100%;
    border: 0
}

.product-wrapper .product-form .product-shipping .shipping-rates-table td,.product-wrapper .product-form .product-shipping .shipping-rates-table th {
    padding: .45rem;
    border: 0
}

.product-wrapper .product-form .product-shipping .shipping-rates-table td {
    font-size: .75rem;
    text-align: center
}

.col-panel b,.col-panel strong,.editComment .ranking,.editDep strong,.lista_presentes b,.lista_presentes strong,.page-register .page-content fieldset b,.page-register .page-content fieldset strong,.product-tabs .tabs-content .payment-tab .option-details table b,.product-tabs .tabs-content .payment-tab .option-details table strong,.product-wrapper .product-form .product-shipping .shipping-rates-table th,.rateBlock .nota strong {
    font-weight: 600
}

.product-wrapper .product-form .product-shipping .shipping-rates-table td:first-child,.product-wrapper .product-form .product-shipping .shipping-rates-table th:first-child {
    width: 130px
}

.product-tabs .tabs-content .payment-tab .option-details table td:first-child,.product-tabs .tabs-content .payment-tab .option-details table th:first-child,.product-wrapper .product-form .product-shipping .shipping-rates-table td:nth-child(2),.product-wrapper .product-form .product-shipping .shipping-rates-table th:nth-child(2) {
    width: 100px
}

.product-wrapper .product-form .product-social-share {
    margin-top: 1.875rem;
    text-align: center
}

@media(min-width: 981px) and (max-width:1200px) {
    .product-wrapper .product-box .product-form,.product-wrapper .product-box .product-gallery {
        width:50%
    }
}

@media(max-width: 980px) {
    .product-wrapper .product-box {
        flex-direction:column
    }

    .product-wrapper .product-box .product-form,.product-wrapper .product-box .product-gallery {
        width: 80%;
        margin: 0 auto
    }

    .product-wrapper .product-box .product-gallery {
        margin-bottom: 40px
    }

    .products-history #produtos ul {
        width: 33%
    }
}

@media(max-width: 460px) {
    .modal .cart-preview-item-image-box {
        padding-left:0!important;
        padding-right: 0!important
    }

    .cart-preview-item-name-box {
        padding-left: 4px!important
    }

    .cart-preview-item-name-box .cart-preview-item-name,.cart-preview-item-price-box {
        font-size: .75rem
    }

    .modal .modal-footer .botao-continuar-comprando,.modal .modal-footer .botao-prosseguir-compra {
        float: none;
        display: block;
        width: 100%;
        text-align: center;
        margin-bottom: 10px
    }

    .modal .modal-footer .botao-prosseguir-compra {
        margin-bottom: 0
    }

    .product-wrapper .product-form .listaVarMultipla img {
        max-width: 38px
    }

    .product-wrapper .product-form #bt_comprar {
        max-width: 100%
    }
}

.section-buy-together .buy-together-loader {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0;
    visibility: hidden
}

.compreJunto form {
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.compreJunto form .fotosCompreJunto {
    width: calc(100% - 245px);
    display: flex;
    padding-right: 40px
}

#coments .board>h2:first-child,.compreJunto>li:nth-child(n+2) {
    margin-top: 40px
}

.compreJunto .produto {
    width: 33.3333%;
    padding: 15px 15px 10px;
    display: flex;
    -webkit-box-orient: vertical;
    -ms-flex-direction: column;
    flex-direction: column;
    border: 1px solid #efefef;
    border-radius: 3px;
    background-color: #fff;
    box-shadow: 0 3px 12px 0 rgba(0,0,0,.05);
    border-radius: var(--border_radius_images)
}

.compreJunto .produto>span {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1;
    overflow: hidden;
    margin-bottom: 24px
}

.compreJunto .produto span>a,.compreJunto .produto span>div {
    display: block;
    position: relative;
    margin-bottom: 10px
}

.compreJunto .produto .product-name {
    color: var(--color_font_dark);
    font-weight: 400
}

.fotosCompreJunto .plus.to:before,.fotosCompreJunto .plus:before,.product-tabs .tabs-content .payment-tab .option a:after {
    font-family: go!important;
    font-weight: 400!important;
    font-style: normal!important;
    font-variant: normal!important;
    text-transform: none!important
}

.compreJunto .produto img {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    opacity: 0;
    visibility: hidden;
    transition: .2s ease-out;
    object-fit: cover;
    height: 100%;
    width: 100%;
    border-radius: var(--border_radius_images)
}

.compreJunto .precoCompreJunto {
    width: 280px;
    padding: 25px;
    text-align: center;
    border: 1px solid #efefef;
    box-shadow: 0 3px 12px 0 rgba(0,0,0,.09)
}

.fotosCompreJunto .plus {
    width: 20px;
    position: relative;
    z-index: 2;
    display: flex;
    font-size: 0;
    align-items: center;
    margin: 0 12px
}

.fotosCompreJunto .plus:before {
    content: "\ea10";
    display: block;
    width: 20px;
    height: 44px;
    border-radius: 3px;
    text-align: center;
    font-size: 1.25rem;
    line-height: 42px;
    color: var(--color_font_dark)
}

.compreJunto .varCont {
    margin-bottom: 10px
}

.compreJunto .varCont input[type=text],.compreJunto .varCont select {
    width: 100%;
    display: block;
    height: 42px;
    padding-left: 14px;
    margin: 0 auto;
    border: 1px solid #ddd;
    background-color: #fff;
    max-width: 100%
}

#coments .submit-review,.botao-commerce.botao-compre-junto:not(.botao-sob-consulta) {
    color: var(--color_font_inverted);
    background-color: var(--color_secondary_medium);
    border-radius: var(--border_radius_buttons)
}

.compreJunto .varTit {
    margin: 10px 0 5px;
    font-weight: 400;
    font-size: .75rem
}

.comprejuto_preco,.comprejuto_preco span {
    font-size: .875rem;
    text-decoration: line-through;
    font-weight: 400;
    color: #999
}

.comprejunto_preco2 {
    font-size: .938rem;
    font-weight: 700
}

.comprejunto_preco2 .PrecoPrincipal {
    color: var(--color_primary_medium);
    font-size: 1.5rem;
    font-weight: 600;
    margin-left: -.3125rem
}

.comprejunto_economize {
    font-size: .75rem;
    font-weight: 500;
    margin: 5px 0 0
}

.botao-commerce.botao-compre-junto:not(.botao-sob-consulta) {
    width: 100%;
    margin: .875rem 0 0;
    padding: 0;
    transition: .2s ease-out
}

.botao-compre-junto:not(.botao-sob-consulta) .botao-commerce-img:before {
    content: "Comprar Junto";
    font-family: var(--font_family);
    font-size: .875rem;
    font-weight: 600;
    line-height: 50px;
    text-transform: uppercase
}

.precoCompreJunto>div:first-child strong {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1.275rem;
    display: block;
    color: var(--color_font_dark)
}

.compre-junto {
    margin-top: 60px
}

.product-wrapper .product-tabs {
    margin: 3rem 0 0
}

.product-tabs .tabs-content .payment-tab {
    position: relative;
    min-height: 75px
}

.product-tabs .tabs-content .payment-tab .option a {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    padding: .9375rem 0;
    font-size: .875rem;
    font-weight: 500;
    border-bottom: 1px solid var(--color_gray_dark)
}

.product-tabs .tabs-content .payment-tab .option a:after {
    content: "\ea01";
    position: absolute;
    top: calc(50% - 4px);
    right: 0;
    margin-left: .3125rem;
    font-size: .75rem;
    transition: .2s ease-out
}

.product-tabs .tabs-content .payment-tab .option.show a:after,.product-tabs.short-tabs .tabs-content .tab-link-mobile.active:after {
    transform: rotate(-90deg)
}

.product-tabs .tabs-content .payment-tab .option a img {
    width: 45px;
    margin-right: .625rem
}

.product-tabs .tabs-content .payment-tab .option-details {
    padding: .9375rem 0 2.5rem
}

.product-tabs .tabs-content .payment-tab .option.show .option-details,.product-tabs.short-tabs .tabs-content .tab.active {
    border-bottom: 1px solid var(--color_gray_dark);
    transition: border .1s ease-out .3s
}

.product-tabs .tabs-content .payment-tab .option-details table {
    width: 100%;
    font-size: .875rem;
    line-height: 1.6
}

.product-tabs .tabs-content .payment-tab .option-details table strong {
    text-transform: lowercase
}

.product-wrapper .product-tabs .tabs-nav {
    display: flex;
    margin-bottom: 0;
    border-bottom: 1px solid var(--color_gray_dark)
}

.product-wrapper .product-tabs .tabs-nav .tab-link {
    display: block;
    font-weight: 500;
    color: var(--color_font_medium);
    text-decoration: none;
    transition: .2s ease-out
}

.product-wrapper .product-tabs .tabs-nav .tab-link .text:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -11px;
    width: 0;
    height: 3px;
    background-color: var(--color_primary_medium);
    transition: .2s ease-out
}

.product-wrapper .product-tabs .tabs-nav .tab-link.active,.product-wrapper .product-tabs .tabs-nav .tab-link:not(.active):hover {
    color: var(--color_font_dark)
}

.product-tabs .tabs-content .payment-tab .option-details table td:nth-child(2),.product-tabs .tabs-content .payment-tab .option-details table th:nth-child(2) {
    width: 400px
}

#coments .product-comments {
    font-size: .875rem;
    display: flex;
    flex-direction: column;
    padding: 15px
}

#coments h2,.modal-store-reviews h2 {
    margin-bottom: 1.875rem;
    font-size: 1.125rem;
    font-weight: 600;
    text-align: center;
    color: var(--color_font_dark)
}

#coments .botao-commerce,.banner-home+.banners-grid,.banner-home.banner-footer-pages {
    margin-top: 20px
}

#coments a[data-logged-user=false] {
    text-align: center;
    display: block;
    text-decoration: none;
    margin: .3125rem auto 0;
    padding: .625rem 1.25rem;
    font-family: var(--font_family);
    font-size: .75rem;
    font-weight: 700;
    color: var(--color_font_inverted)!important;
    background-color: var(--color_secondary_medium);
    border-radius: 4px;
    transition: .2s ease-out;
    text-transform: uppercase;
    max-width: 250px
}

#coments #form-comments {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 500px;
    margin: 0 auto
}

#coments #form-comments label {
    margin-bottom: .9375rem
}

#coments #form-comments #email_coment,#coments #form-comments #nome_coment {
    display: block;
    width: 100%;
    margin: 5px 0 0
}

#coments #form-comments h5 {
    margin-top: -.625rem
}

#coments #form-comments h3 {
    font-size: .875rem;
    margin: 1.25rem 0 0;
    font-weight: 400
}

#coments .submit-review {
    width: 160px;
    height: 42px;
    margin: 1.25rem auto 0;
    font-family: var(--font_family);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    transition: .2s ease-out
}

#produto_comprar .precoAvista,.products-history .ValoresLista .precoAvista,.products-history .ValoresLista .precode,.products-history .ValoresLista strong {
    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif
}

#coments .blocoSucesso {
    margin: 0;
    text-align: center
}

.rateBlock {
    margin-top: .3125rem
}

#coments .ranking .icon,.rateBlock .starn {
    display: block;
    width: .938em;
    height: .938em;
    margin-right: .1875rem;
    background: url(../img/empty-star.svg) 0 0/100% no-repeat;
    cursor: pointer
}

#coments .ranking .rating {
    width: 85px
}

#coments .board {
    display: flex;
    flex-direction: column
}

#coments .blocoSucesso~* {
    order: -1
}

.hreview-comentarios {
    margin-bottom: 1.25rem;
    padding: 1.25rem;
    background-color: rgba(0,0,0,.03)
}

#container-add-lista #add-listas,.btns-paginator {
    background-color: var(--color_gray_dark);
    transition: .2s ease-out
}

.hreview-comentarios h4 {
    font-size: .75rem;
    font-weight: 400
}

.hreview-comentarios h3 {
    color: var(--color_primary_medium);
    font-size: 1rem!important;
    font-weight: 700!important
}

.hreview-comentarios .ranking {
    display: flex;
    align-items: center;
    margin: .625rem 0;
    font-weight: 500
}

.hreview-comentarios .description {
    margin: 5px 0;
    font-size: .875rem
}

.section-product-related {
    margin-left: -10px;
    margin-right: -10px
}

.section-product-related .swiper-container {
    padding-bottom: 40px;
    margin-bottom: -40px
}

.products-history {
    margin-top: 3rem
}

.products-history .visitados_produtos {
    display: flex;
    align-items: center;
    flex-direction: column
}

.products-history #produtos {
    display: flex;
    margin-left: -10px;
    flex-wrap: wrap
}

.products-history #produtos ul {
    padding: 7px 10px;
    margin: 10px;
    border: 1px solid var(--color_gray_medium);
    display: flex;
    align-items: center;
    border-radius: var(--border_radius_images)
}

.products-history #produtos ul li {
    width: 320px;
    align-items: center;
    display: grid;
    grid-template-columns: repeat(3,1fr);
    grid-template-rows: repeat(2,1fr);
    grid-column-gap: 0;
    grid-row-gap: 0
}

.products-history #produtos ul li>a {
    grid-area: 1/1/3/2;
    height: 90px;
    width: 90px;
    display: block
}

.products-history #produtos ul li h3 {
    grid-area: 1/2/2/4
}

.products-history #produtos ul li .ValoresLista {
    grid-area: 2/2/3/4;
    margin-bottom: 10px
}

.products-history #produtos ul li:after {
    content: "";
    clear: both;
    display: block
}

.products-history #produtos ul li .ValoresLista div#precoDe {
    display: inline-block;
    margin-right: 1px
}

.products-history .ValoresLista .precode {
    text-decoration: line-through
}

.products-history .ValoresLista .precoAvista {
    font-weight: 700;
    color: var(--color_font_price);
    font-size: 14px
}

.products-history #produtos img {
    display: block;
    margin: auto;
    width: 100%;
    height: 100%;
    object-fit: scale-down;
    object-position: center
}

.products-history .NomeProdLista {
    color: var(--color_font_dark);
    font-size: .875rem;
    margin: 0;
    line-height: 1.3;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden
}

.products-history .ValoresLista img[src*=sobconsulta]+.botao-commerce {
    display: inline;
    padding: .3125rem .625rem;
    font-size: .75rem
}

.products-history .ValoresLista img[src*=sobconsulta]+.botao-commerce:hover {
    background-color: var(--color_primary_medium)
}

.products-history .products-history-wrapper .history-loader {
    position: absolute;
    min-height: 70px;
    display: none
}

.paginacao_ajax {
    order: 2;
    margin-top: 15px
}

.products-history .paginacao_ajax_prod {
    display: block;
    text-align: center;
    margin-top: 10px;
    font-size: 14px
}

#linksPag a {
    display: inline-block;
    line-height: 26px;
    width: 26px;
    text-align: center;
    color: #fff;
    background: var(--color_primary_medium);
    padding: 0 4px;
    margin: 0 4px;
    border-radius: var(--border_radius_buttons)
}

#linksPag a:not(.pageON) {
    background: 0;
    color: var(--color_primary_medium)
}

.page-busca_noticias h1 {
    margin-bottom: 1.875rem;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--color_font_dark)
}

.btns-paginator,.editDep>li,.wishlist {
    color: var(--color_font_medium)
}

#listaCategoriasNoticias {
    display: flex;
    margin-bottom: 1.875rem;
    font-size: 0
}

#listaCategoriasNoticias li a {
    margin-right: 1.25rem;
    font-size: 1rem;
    font-weight: 500;
    transition: .2s ease-out
}

#listaCategoriasNoticias li a b,#listaCategoriasNoticias li a strong {
    color: var(--color_primary_medium);
    font-weight: 500
}

.page-busca_noticias .board .left {
    margin: 20px 0 0;
    font-size: .875rem
}

.page-busca_noticias .noticias {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -.625rem
}

.page-busca_noticias .noticias li {
    width: 25%;
    padding: 1.25rem .625rem
}

@media(max-width: 800px) {
    .list-product:not(.swiper-wrapper) .item,.page-busca_noticias .noticias li {
        width:50%
    }
}

.page-noticia .board .dataNoticia+h2 {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 1.875rem;
    color: var(--color_font_dark);
    text-decoration: none
}

.page-noticia .board img {
    float: left;
    max-width: 100%;
    margin-bottom: 15px;
    margin-right: 30px;
    border-radius: 8px;
    height: auto!important
}

.page-noticia .board p {
    font-size: .875rem;
    margin-bottom: 15px
}

.page-busca_noticias .board+.container3 .right {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1.875rem
}

.btns-paginator {
    display: block;
    min-width: 44px;
    height: 38px;
    margin: .3125rem;
    font-size: .875rem;
    font-weight: 700;
    line-height: 38px;
    text-align: center;
    border-radius: 2px
}

.btns-paginator.btn-pagina-anterior,.btns-paginator.btn-primeira-pagina,.btns-paginator.btn-proxima-pagina,.btns-paginator.btn-ultima-pagina {
    font-size: 0;
    display: flex;
    align-items: center;
    justify-content: center
}

.btns-paginator.btn-pagina-anterior:before,.btns-paginator.btn-primeira-pagina:after,.btns-paginator.btn-primeira-pagina:before {
    content: "\ea02";
    font-family: go!important;
    font-size: .625rem;
    font-style: normal!important;
    font-weight: 400!important;
    font-variant: normal!important;
    text-transform: none!important
}

.btns-paginator.btn-proxima-pagina:before,.btns-paginator.btn-ultima-pagina:after,.btns-paginator.btn-ultima-pagina:before {
    content: "\ea03";
    font-family: go!important;
    font-size: .625rem;
    font-style: normal!important;
    font-weight: 400!important;
    font-variant: normal!important;
    text-transform: none!important
}

.store-review-paginator {
    display: flex;
    flex-wrap: wrap;
    margin: 0 0 1.5rem
}

.page-depoimentos .page-content {
    min-height: 350px
}

.page-depoimentos .board h1,.page-listas_index .board h2,.page-newsletter .board h1 {
    margin-bottom: 1.875rem;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--color_font_dark)
}

.editDep {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between
}

.editDep>li {
    position: relative;
    width: calc(50% - 20px);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.25rem;
    text-align: center;
    background-color: #fff;
    z-index: 0;
    border: 1px solid var(--color_gray_medium);
    border-radius: var(--border_radius_images)
}

.editDep h3 {
    order: 2;
    margin: .625rem 0 0;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.5;
    text-indent: -54px;
    overflow: hidden
}

.editDep h3 img {
    position: absolute;
    top: 16px;
    left: 20px;
    bottom: 0;
    z-index: -1;
    opacity: .7
}

.editDep ul li.dep {
    font-size: .875rem;
    line-height: 1.5
}

#depoimento span.error-block {
    font-size: 12px;
    font-weight: 400;
    color: #b00000
}

#depoimento label {
    display: block;
    margin-bottom: 14px
}

#depoimento .nota_dep label {
    display: inline-block;
    margin-left: 5px
}

#depoimento span.block {
    margin: 5px 0;
    display: block
}

#depoimento input[size],#depoimento input[type=email],#depoimento input[type=text],#depoimento textarea {
    width: 100%;
    display: block;
    margin-top: 5px
}

#depoimento #aviso_depoimento {
    margin: 0;
    display: block!important
}

.page-depoimentos .page-content .botao-commerce {
    max-width: 200px;
    width: 100%;
    margin: 30px auto;
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
    cursor: pointer
}

.modal-store-reviews .botao-commerce {
    display: block;
    width: 180px;
    margin: 1.875rem auto 0;
    font-weight: 600;
    text-align: center;
    text-transform: uppercase
}

.modal-store-reviews .blocoSucesso {
    text-align: center;
    margin: 0
}

.wishlist {
    padding: 5px
}

.wishlist a {
    color: inherit;
    transition: .2s ease-out
}

#container-add-lista a {
    font-size: 0;
    display: flex
}

#container-add-lista a:before {
    content: "\ea0a";
    font-family: go!important;
    font-size: .9375rem;
    margin-right: .25rem;
    text-transform: none!important
}

#container-add-lista #bloco-add-lista>a {
    color: var(--color_primary_medium);
    align-items: center
}

#container-add-lista #bloco-add-lista>a img {
    margin-left: 3px
}

#container-add-lista #add-listas {
    display: block!important;
    position: absolute;
    width: 170px;
    top: 1.5625rem;
    padding: .625rem;
    color: var(--color_font_dark);
    font-size: .75rem;
    font-weight: 400;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0,0,0,.11);
    opacity: 0;
    visibility: hidden
}

#container-add-lista #add-listas:before {
    content: "";
    position: absolute;
    top: -4px;
    left: 50%;
    width: 8px;
    height: 4px;
    background-color: var(--color_primary_medium);
    clip-path: polygon(50% 0,0 100%,100% 100%);
    transform: translateX(-50%)
}

#container-add-lista .listas a:before {
    margin-right: .4375rem;
    color: var(--color_font_medium)
}

@media(min-width: 1001px) {
    #container-add-lista #add-listas {
        left:50%;
        transform: translateX(-50%)
    }
}

#tipos-listas h3 {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: .625rem;
    text-align: center
}

.page-listas_busca h2,.page-listas_evento h2 {
    font-size: 18px;
    font-weight: 500;
    color: var(--color_font_dark)
}

.page-listas_evento table.busca {
    margin-top: .9375rem
}

.page-listas_busca .titCatalogo {
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--color_font_dark)
}

#acceptTerm,#produto_comprar a:before,.bts,.bts2,.checkbox-accept-terms input:checked~label:before,.lista-produtos .lista-produto-comprar,.page-register #CadastroAbas li.aberta a {
    background-color: var(--color_secondary_medium);
    color: var(--color_font_inverted)
}

.page-listas_busca h2~*,.page-listas_busca h2~* strong {
    font-size: .875rem;
    font-weight: 400
}

.page-listas_busca h2~ul li {
    position: relative;
    padding: 1.25rem;
    border: 1px solid var(--color_gray_dark)
}

.page-listas_busca h2~ul li .NomeProduto {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color_font_dark)
}

.page-listas_busca h2~ul li a[href*="lista.php"] {
    position: absolute;
    right: 20px;
    top: calc(50% - 13px)
}

#lista_criar h2 {
    margin-top: 10px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase
}

#lista_criar input[size="40"],#lista_criar input[size="50"] {
    width: 100%;
    max-width: 300px
}

.page-listas_criar .board td {
    padding: 10px;
    word-break: break-all
}

.page-print_lista .page-content h1 {
    font-size: 1.375rem;
    text-transform: uppercase;
    font-weight: 600;
    margin: 10px 0
}

#vitrine-catalogo h3,.page-print_lista .page-content h2 {
    font-size: 1rem
}

.lista-produtos {
    text-align: center;
    display: inline-block;
    max-width: 340px;
    margin: 15px;
    width: 100%
}

.lista-produtos .lista-produto-comprar {
    position: relative;
    width: 100%;
    max-width: 200px;
    height: 42px;
    margin: .625rem auto;
    border-radius: 3px;
    transition: .2s ease-out
}

.lista-produtos .lista-produto-comprar:before {
    content: "Comprar";
    line-height: 40px;
    font-size: .875rem;
    text-transform: uppercase;
    font-weight: 600
}

.lista-produtos .lista-produto-comprar input[type=image] {
    width: 100%;
    height: 42px;
    top: 0;
    left: 0;
    position: absolute;
    opacity: 0
}

.page-lista .board,.page-lista .board hr,.page-navegacao_visitados hr {
    margin: 20px 0
}

.lista-produtos .qntd {
    border: 1px solid #e5e5e5;
    width: 80px;
    display: block;
    margin: 5px auto;
    height: 35px;
    text-align: center
}

.page-navegacao_visitados h2 {
    font-size: 1.125rem;
    margin: 20px 0
}

.bts,.bts2 {
    padding: 4px 8px;
    height: 23px;
    text-align: center;
    line-height: 14px;
    font-size: .75rem;
    display: inline-block;
    border-radius: 20px;
    margin: 0 .3125rem;
    transition: .2s ease-out
}

.vitrineVisitados {
    margin: 0 -10px;
    display: flex;
    font-size: .875rem
}

.vitrineVisitados>li {
    padding: 10px;
    width: 33.33333%
}

.nomeProd {
    margin-bottom: 10px;
    display: block
}

.ranking.hreview-aggregate {
    margin: 5px 0
}

.editComment li>a {
    display: block;
    width: 90px;
    height: 90px
}

.editComment li>a img {
    display: block;
    margin: auto
}

.editComment li {
    margin: 20px 0;
    display: flex;
    position: relative;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.editComment h3 {
    font-size: .75rem;
    font-weight: 600;
    position: absolute;
    width: calc(100% - 100px);
    left: 100px;
    top: 0
}

.editComment .bts {
    margin: 0 0 5px
}

.editComment .relativity {
    width: 100%;
    padding-top: 25px
}

.editComment .change {
    font-size: .75rem;
    margin-top: 5px
}

.consulta-product {
    font-size: .75rem;
    font-weight: 600;
    margin-bottom: 10px
}

.applied-filters {
    margin: 10px 0
}

.applied-filters a {
    color: var(--color_primary_medium);
    font-weight: 600
}

.applied-filters a svg {
    width: 8px;
    height: 8px;
    fill: inherit;
    margin-left: 10px
}

.filters-list a {
    display: inline-block;
    padding: 5px 0;
    line-height: 1;
    -webkit-transition: color .2s ease-in-out;
    -o-transition: color .2s ease-in-out;
    transition: color .2s ease-in-out
}

.filters-list label {
    cursor: pointer;
    padding: 5px 0
}

.space-radio {
    padding: 3px 0
}

.filters-list input[type=checkbox] {
    position: absolute;
    left: 0;
    z-index: -1;
    opacity: .001;
    width: 0;
    height: 0
}

.filter-button {
    margin: 10px 0;
    width: 100%
}

.icon-radio {
    width: 18px;
    height: 18px;
    border: 1px solid #707070;
    opacity: .5;
    position: relative;
    margin-right: 14px;
    display: block
}

.icon-radio:before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 10px;
    height: 10px;
    background-color: #707070;
    opacity: 0;
    -webkit-transition: .3s ease-in-out;
    -o-transition: .3s ease-in-out;
    transition: .3s ease-in-out
}

.col-content .breadcrumb {
    margin: 0 0 22px
}

:not(.container2)>.board>h1 {
    font-size: 2rem;
    text-transform: uppercase;
    color: var(--color_font_dark)
}

.contato-telefones .block:before,tray-login .tray-close:before {
    font-family: go!important;
    font-weight: 400!important;
    text-transform: none!important;
    font-style: normal!important;
    font-variant: normal!important
}

.page-navegacao_visitados .vitrineVisitados span.valores span.txt-por,.page-search #Vitrine,.product-wrapper .product-form .product-main-info .product-details .product-details__item {
    font-size: 14px
}

#vitrine-catalogo {
    padding: 30px 0 0
}

.page-search h1.color {
    margin-top: 30px;
    text-align: center;
    color: var(--color_font_dark)
}

.page-search #Vitrine fieldset {
    border: 0;
    padding: 0;
    margin: 14px 0
}

.page-search #Vitrine fieldset p {
    margin: 30px 0
}

.page-search #Vitrine fieldset input[name=palavra_busca] {
    display: block;
    margin: 5px 0
}

.page-search #Vitrine form input[type=checkbox] {
    margin: 7px 5px 7px 0
}

.page-search #Vitrine #preco_ini {
    margin-right: 15px
}

.page-search #Vitrine form {
    max-width: 600px;
    margin: 0 auto
}

.tray-container,.tray-container__identify {
    left: calc(50% - 160px);
    width: 100%!important;
    max-width: 320px!important;
    padding: 2.5rem!important;
    border-radius: 4px
}

tray-login .tray-close {
    position: absolute!important;
    top: 5px!important;
    right: 5px!important;
    width: 40px!important;
    height: 40px!important;
    font-size: 0!important;
    border: none!important;
    cursor: pointer;
    box-shadow: 0 3px 5px rgba(0,0,0,.15)!important
}

tray-login .tray-close:before {
    content: "\ea17";
    font-size: 1rem;
    color: var(--color_primary_medium)
}

tray-login .tray-title {
    font-family: var(--font_family);
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--color_font_dark);
    text-align: center
}

.caixa-cadastro h3,.caixa-login h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color_font_dark)
}

.caixa-cadastro,.caixa-login {
    border: 1px solid var(--color_gray_dark);
    max-width: 550px;
    width: 100%;
    margin: 0 30px 40px;
    padding: 30px;
    border-radius: 8px
}

.caixa-cadastro fieldset,.caixa-login fieldset {
    border: 0;
    padding: 0
}

.caixa-cadastro p:first-child {
    display: block;
    margin: .625rem 0 1.875rem;
    width: 100%
}

.obriga.red:before {
    content: "*";
    font-size: .75rem
}

.page-register .board h1 {
    text-align: center;
    margin: 0 0 1.25rem;
    color: var(--color_font_dark)
}

.page-register #frm2 fieldset {
    text-align: left
}

.page-register #CadastroAbas {
    text-align: center;
    margin: 0 0 20px
}

.page-register #CadastroAbas li {
    display: inline-block;
    margin: 0 10px 5px
}

.page-register #CadastroAbas li a {
    border: 1px solid var(--color_gray_dark);
    font-size: .875rem;
    width: 137px;
    text-align: center;
    -webkit-transition: .3s ease-in-out;
    -o-transition: .3s ease-in-out;
    transition: .3s ease-in-out;
    display: inline-block;
    line-height: 40px;
    border-radius: 3px
}

.checkbox-accept-terms input:checked~label:before,.page-register #CadastroAbas li.aberta a {
    border-color: var(--color_secondary_medium)
}

.page-register .page-content fieldset {
    border: 0;
    margin: 20px auto 0;
    width: 100%;
    max-width: 450px;
    border: 1px solid var(--color_gray_dark);
    padding: 20px;
    font-size: .938rem;
    border-radius: 8px
}

.page-register .topBorder+.board {
    text-align: center;
    margin: 20px 0 0
}

#fisica,#juridica {
    margin: 20px 0 0
}

.page-register .page-content fieldset label {
    display: block;
    margin: 5px 0 0
}

.page-register fieldset input[type=email],.page-register fieldset input[type=password],.page-register fieldset input[type=tel],.page-register fieldset input[type=text],.page-register fieldset select {
    margin: 4px 0 0;
    transition: .3s ease-in-out
}

.page-register fieldset input[type=email]:focus,.page-register fieldset input[type=password]:focus,.page-register fieldset input[type=tel]:focus,.page-register fieldset input[type=text]:focus {
    box-shadow: 0 2px 5px rgba(0,0,0,.1)
}

.blocoAlerta,.cart-preview .cart-preview-item-error {
    padding: .5rem;
    font-size: .75rem;
    text-align: center;
    border-radius: 4px;
    color: #e15656;
    background-color: #ffebeb;
    margin: 5px 0;
    display: block
}

#NavLogoTray a:after,#form_presentes .botao-commerce,#produto_comprar a:before,.col-panel .icoFilho,.mensagensErro,.page-navegacao_visitados .vitrineVisitados span.valores span,.sidebar-central a {
    display: inline-block
}

.mensagensErro {
    color: #da0808;
    font-size: .75rem;
    padding: 4px 0
}

.sidebar-central {
    width: 250px;
    margin-right: 30px
}

.sidebar-central>div>ul>li {
    margin-bottom: 1.25rem
}

.sidebar-central h4 {
    font-weight: 600;
    font-size: 1.12rem;
    margin: 10px 0 4px;
    color: var(--color_font_dark)
}

.sidebar-central a {
    font-size: .875rem;
    margin: 4px 0;
    color: inherit;
    transition: .2s ease-out
}

.central-menu {
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin: 0 -10px
}

.col-panel {
    width: calc(100% - 280px);
    font-size: .875rem
}

.col-panel h1,.col-panel h2 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 10px;
    color: var(--color_font_dark)
}

.col-panel h1 {
    border-bottom: 1px solid var(--color_gray_dark);
    padding-bottom: .625rem
}

.col-panel .icoPai {
    width: 100%;
    margin: 20px 0 0;
    padding: 0 10px
}

.col-panel .icoFilho {
    padding: 0 10px;
    text-align: center
}

.icoFilho .bgcolor {
    background: var(--color_font_medium)
}

@media(max-width: 550px) {
    #letmeknow_response label,#nao_disp label {
        width:100%;
        max-width: unset
    }

    #letMeKnow:before {
        width: 100%;
        text-align: center
    }

    form[action*=central_premiacao_historico] input {
        width: 100%;
        display: block;
        max-width: 300px;
        margin-bottom: 10px
    }
}

.central-icons {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -ms-flex-direction: column;
    flex-direction: column
}

.central-icons a {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1;
    display: block;
    padding-bottom: 20px;
    position: relative;
    z-index: 2;
    width: 100%;
    text-align: center
}

.central-icons p {
    margin-top: -15px
}

.col-panel .tablePage {
    border: 1px solid #e4e4e4
}

.col-panel .tablePage td,.col-panel .tablePage th {
    border: 1px solid #e4e4e4;
    padding: 5px
}

.table-overflow {
    overflow: auto;
    width: 100%
}

.page-contact h1 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
    color: var(--color_font_dark)
}

.page-contact h1+p.description {
    margin-bottom: 50px;
    font-size: 1.125rem
}

.page-contact .page-content h3 {
    margin: 0 0 .625rem;
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--color_font_dark)
}

.page-contact .board>p {
    font-size: 1rem;
    margin: 0 0 24px;
    line-height: 1.38
}

.page-contact .cols .box-form {
    width: 60%;
    margin-right: 5%
}

.page-contact .cols .info-form {
    width: 35%;
    padding-left: 30px
}

.page-contact .cols .info-form .board {
    margin-bottom: 1.875rem;
    font-size: .875rem
}

.page-contact .page-content label.block {
    display: block;
    margin: 0 0 20px
}

.page-contact .page-content .block span.block {
    display: block;
    margin: 0 0 4px;
    color: #313438;
    font-weight: 500;
    font-size: .875rem
}

.page-contact .page-content input.text {
    height: 42px
}

.page-contact .page-content .textarea,.page-contact .page-content input.text {
    width: 100%;
    max-width: 100%
}

.page-contact .page-content .box-captcha #captcha-loader {
    margin-top: 8px
}

.page-contact .page-content input#texto_captcha {
    width: calc(100% - 126px);
    margin-left: 20px
}

.contato-telefones .block:before {
    vertical-align: middle;
    margin-right: 5px;
    color: var(--color_primary_medium)
}

.contato-telefones .block:first-child:before {
    content: "\ea0e";
    font-size: 1.75rem
}

.contato-telefones .block:nth-child(2):before {
    content: "\ea1a";
    font-size: 1.5625rem
}

.page-contact .blocoSucesso {
    color: #28be48!important;
    padding: 10px 0
}

.page-contact .board .botao-commerce {
    width: 100%;
    height: 42px;
    max-width: 180px;
    padding: 0;
    font-size: .875rem;
    font-weight: 600;
    text-transform: uppercase
}

.page-contact .page-content .msg-obriga {
    display: block;
    margin: 5px 0 20px
}

.footer {
    background: var(--color_footer_bg);
    margin-top: 1.875rem;
    padding: 0;
    color: var(--color_footer_text)
}

.footer .footer-main .logo img {
    display: block;
    height: 100%;
    max-height: 64px
}

.footer .footer-main .social-media a {
    font-size: 22px;
    transition: .2s ease-out;
    margin: 0 .625rem
}

.footer .footer-main .newsletter {
    width: 100%;
    padding: 25px 0
}

.footer .footer-main .newsletter .info {
    flex: 1;
    justify-content: flex-start;
    text-align: left
}

.footer .footer-main .newsletter .info .icon {
    color: var(--color_newsletter_text);
    margin-right: 20px
}

.footer .footer-main .newsletter .info .icon svg {
    width: 50px;
    height: 50px;
    fill: currentColor
}

.footer .footer-main .newsletter .text .first {
    font-weight: 700;
    color: var(--color_newsletter_text);
    font-size: 21px;
    font-family: var(--font_family_title)
}

.footer .footer-main .newsletter .text .last {
    font-weight: 400;
    color: var(--color_newsletter_text);
    font-size: 17px
}

.footer .footer-main .newsletter .form {
    width: 60%;
    padding-left: 1.25rem;
    position: relative;
    display: flex
}

.footer .footer-main .newsletter .form .field {
    height: 50px;
    font-weight: 400;
    padding: 0 0 0 20px;
    font-size: .875rem;
    background: #fff;
    border-radius: var(--border_radius_buttons);
    border: 1px solid var(--color_footer_border);
    color: #333;
    width: 100%
}

.footer .footer-main .newsletter .form .field::placeholder {
    color: currentColor
}

.footer .footer-main .newsletter .form .news-button {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    font-size: 13px;
    transition: .2s ease-out;
    font-weight: 700;
    text-transform: uppercase;
    font-family: var(--font_family);
    margin-left: 10px;
    border-radius: var(--border_radius_buttons);
    background: var(--color_newsletter_bg_button);
    color: var(--color_newsletter_text_button);
    padding: 0 20px;
    flex: 0 0 auto;
    width: auto
}

@media(max-width: 500px) {
    .header .header-search-wrapper .input-search {
        height:40px;
        text-transform: inherit;
        font-weight: 400
    }

    .product-wrapper .product-form #quant {
        font-size: 16px
    }

    .footer .footer-main .newsletter .form .field {
        font-size: 14px;
        height: 45px;
        padding-left: 15px
    }
}

@media(max-width: 370px) {
    .footer .footer-main .newsletter .form .field {
        font-size:13px;
        height: 45px;
        padding-left: 10px
    }
}

.footer .cols .container {
    padding: 40px 5px;
    flex-wrap: nowrap
}

.footer .logo-box img {
    max-height: 44px;
    width: auto;
    height: auto;
    max-width: 260px
}

.footer .social-media .icon {
    margin-right: 18px;
    transition: .2s ease-out
}

.footer .title {
    font-size: 19px;
    font-weight: 700;
    margin-bottom: 8px;
    font-family: var(--font_family_title)
}

.footer .payment-list+.title {
    margin-top: 15px
}

.footer .list a {
    font-size: .87rem;
    font-weight: 400;
    display: block;
    line-height: 1.5;
    padding: 5px 0;
    transition: .2s ease-out
}

.footer .box-infos a {
    padding-bottom: 5px;
    display: flex;
    align-items: center;
    line-height: 15px;
    word-break: break-word;
    justify-content: left
}

.footer .list .icon,.footer .social-media .icon {
    font-size: 25px
}

.footer .payment-list li img {
    max-width: 37px;
    max-height: 22px;
    opacity: 0;
    visibility: hidden;
    transition: .2s ease-in-out;
    display: block;
    margin: 0 6px 6px 0
}

.footer .security-seals {
    gap: 10px
}

.footer .security-seals li {
    display: flex;
    align-items: center;
    margin: 0
}

.footer .security-seals li img {
    max-width: 105px;
    object-fit: contain
}

.footer .security-seals li a,.footer .security-seals li div {
    display: flex;
    height: 100%
}

.google-seal a {
    font-size: 24px;
    display: flex;
    align-items: center
}

.google-seal .icon-shield {
    color: #2aa647;
    margin-right: 2px
}

.footer .copy {
    font-size: .75rem;
    font-weight: 500;
    text-align: center;
    padding: .8rem 0;
    border-top: 1px solid var(--color_footer_border);
    background: #fff;
    color: #333
}

#NavLogoTray {
    text-align: center;
    font-size: .875rem
}

#NavLogoTray a {
    transition: .2s ease-out;
    font-weight: 600;
    display: flex;
    align-items: center;
    font-size: 0;
    filter: grayscale(1)
}

#NavLogoTray a:hover,.footer .copy .tray .credits-partner a:hover {
    filter: none;
    opacity: .8
}

.product-wrapper .product-form .actions .page-product__quantity-and-buy {
    display: flex;
    justify-content: space-between
}

.product-wrapper .product-form .actions .page-product__quantity-and-buy .page-product__quantity {
    width: 130px;
    margin-right: 10px;
    position: relative
}

.product-wrapper .product-form .actions .page-product__quantity-and-buy .page-product__button-buy {
    width: 100%
}

.product-wrapper .product-form .actions .page-product__quantity-and-buy .page-product__quantity .plus-minus input {
    position: absolute;
    width: 20px;
    height: 20px;
    top: calc(50% - 10px);
    left: 8px;
    font-size: 25px;
    line-height: 15px;
    cursor: pointer;
    color: var(--color_secondary_medium);
    background: 0 0!important;
    box-shadow: none!important
}

.product-wrapper .product-form .actions .page-product__quantity-and-buy .page-product__quantity .plus-minus input#plus {
    left: initial;
    right: 8px;
    font-size: 17px;
    top: calc(50% - 9px)
}

.footer .cols .container>.box.box-infos .flex,.product-wrapper .product-form .product-gifts ul#brindes {
    justify-content: flex-start
}

.product-wrapper .product-form .product-gifts ul#brindes>li {
    width: 15%
}

.product-wrapper .product-form .product-gifts ul#brindes>li img {
    max-width: 100%;
    border: 1px solid #ddd!important;
    border-radius: var(--border_radius_images);
    width: 100%;
    height: 100%;
    object-fit: cover
}

@media(min-width: 1000px) and (max-width:1230px) {
    .footer .footer-main .newsletter .info {
        width:60%
    }
}

@media(min-width: 575px) and (max-width:767px) {
    .footer .footer-main .newsletter .info {
        justify-content:center
    }
}

.lista_presentes th {
    font-size: .875rem;
    font-weight: 600;
    padding: .6rem
}

.lista_presentes td {
    padding: .6rem
}

.lista_presentes select.select {
    width: 100%;
    margin-bottom: .65rem;
    min-width: 295px
}

.lista_presentes td a {
    width: 100px;
    display: block;
    float: left
}

.lista_presentes td a+strong {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 104px);
    font-size: .75rem;
    max-width: 250px
}

.lista_presentes textarea {
    border: 1px solid #e4e4e4;
    padding: 14px;
    border-radius: 0;
    color: #565656;
    width: 100%!important;
    display: block;
    margin-top: .25rem
}

.lista_presentes li:last-child {
    margin-bottom: .938rem
}

#form_presentes .relative-button {
    float: right
}

@media(max-width: 991px) {
    .page-catalog .page-content,.page-content:not(.not-padding),.page-search .page-content {
        padding-top:15px
    }

    .hide-menu,.lista_presentes li:not([class]):first-child {
        display: none
    }

    .col-panel {
        width: 100%;
        margin: 20px 0
    }

    .sidebar-central {
        width: 100%;
        margin: 0
    }

    .line-panel {
        display: block
    }

    #form_presentes {
        display: block;
        max-width: 450px;
        width: 100%;
        margin: auto
    }

    .lista_presentes td {
        display: block;
        width: 100%!important
    }
}

.page-content .style1,.page-content .style4 {
    font-size: .875rem!important;
    color: var(--color_font_medium)
}

.page-extra .table-overflow .table-overflow table tr[bgcolor="#e7e7e7"] {
    background: var(--color_gray_medium)
}

.page-extra .page-content>.container>h1 {
    background: 0;
    font-size: 2em;
    height: auto;
    border: 0;
    color: var(--color_font_medium);
    padding-left: 0
}

.campoform:not([type=checkbox]) {
    margin: .675rem 0
}

.page-extra table[style="width: 700px"] {
    width: 100%!important
}

#concorda-termo {
    margin-top: 1.875rem;
    padding-top: 1.875rem;
    border-top: 1px solid var(--color_gray_dark)
}

#acceptTerm {
    display: block;
    padding: .5rem .625rem;
    font-size: .75rem;
    font-weight: 700;
    text-transform: uppercase;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
    transition: .2s ease-out
}

.checkbox-accept-terms label {
    position: relative;
    display: block;
    padding-left: 1.875rem;
    cursor: pointer;
    font-size: .875rem;
    font-weight: 500;
    color: var(--color_font_medium)
}

.checkbox-accept-terms label:before {
    pointer-events: none;
    background-color: #fff;
    border: 1px solid var(--color_gray_dark);
    border-radius: 2px;
    transition: .2s ease-in-out
}

.checkbox-accept-terms label:after,.checkbox-accept-terms label:before {
    position: absolute;
    content: "";
    top: .125rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem
}

.checkbox-accept-terms label:after {
    background: 50%/50% 50% no-repeat
}

.progressive {
    margin: 10px 0;
    font-size: .75rem;
    text-align: center
}

.cart__empty,.cart__title {
    font-size: 1.42857rem
}

.banner-header img {
    display: block;
    margin: auto;
    max-width: 100%
}

@media(max-width: 1024px) {
    .catalog-footer {
        flex-direction:column-reverse
    }

    .catalog-footer .results {
        width: 100%;
        text-align: center;
        margin-top: 15px
    }

    .box-gallery .image-show img:nth-child(2) {
        display: none!important
    }

    .banner-header {
        display: none
    }
}

#ProdAbas li {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color_font_dark);
    text-align: center;
    margin-bottom: 40px
}

.page-noticia #ProdBlock.prodBox {
    display: block!important;
    margin-top: 40px
}

#listaNoticiaProdutos {
    display: flex;
    flex-wrap: wrap
}

#listaNoticiaProdutos li {
    width: 25%;
    position: relative
}

#produto_imagem {
    float: left
}

#produto_imagem~* {
    margin-left: 100px
}

#produto_imagem img {
    max-width: 90px
}

#produto_comprar a:before {
    content: "Ver detalhes";
    padding: 7px 14px;
    font-weight: 700;
    font-size: .75rem;
    text-transform: uppercase;
    border-radius: 3px;
    margin: 5px 0 6px;
    transition: .2s ease-out
}

#produto_comprar a:after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%
}

#produto_comprar .precoAvista {
    font-weight: 700;
    color: var(--color_primary_medium);
    font-size: 14px
}

#produto_dados h3 {
    color: var(--color_font_dark);
    font-size: .875rem;
    margin: 10px 0;
    line-height: 1.3;
    display: block;
    font-weight: 500
}

@media(max-width: 1200px) {
    #listaNoticiaProdutos li {
        width:33.33333%;
        margin-bottom: 20px
    }
}

@media(max-width: 767px) {
    .footer .footer-main .newsletter .form .field[name=name] {
        width:calc(40% - 0)
    }

    .old-browser .container {
        padding: 0 20px
    }

    .modal .modal-title {
        font-size: .75rem;
        padding: 0 .875rem
    }

    .compreJunto .precoCompreJunto,.modal-dialog-center,.products-history #produtos ul,.vitrineVisitados>li {
        width: 100%
    }

    .container {
        padding: 0 15px
    }

    .row {
        margin: 0 -.625rem
    }

    .row .col {
        padding: 0 .625rem
    }

    .page-comparador .page-content>.container>.board>.container3>.container2>.board {
        flex-direction: column-reverse
    }

    .page-comparador .page-content>.container>.board>.container3>.container2>.board .left {
        margin-top: 15px
    }

    .modal-theme .modal-wrapper {
        height: auto
    }

    .swiper-carousel .next,.swiper-carousel .prev,.swiper-container .next,.swiper-container .prev {
        display: none;
        width: 35px;
        height: 35px;
        font-size: 13px
    }

    .floating-whatsapp.on-left {
        left: 10px
    }

    .floating-whatsapp.on-right {
        right: 10px
    }

    .header-menu {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex-grow: 0;
        flex-shrink: 0;
        width: 20px;
        height: 18px;
        margin-right: 18px;
        cursor: pointer
    }

    .list-product,.section-product-related {
        margin-right: 0;
        margin-left: 0
    }

    .header-menu div {
        width: 100%;
        height: 2px;
        background-color: var(--color_header_text)
    }

    .header .cart-toggle .cart-quantity {
        width: 17px;
        height: 18px;
        font-size: 11px;
        line-height: 18px;
        top: -4px
    }

    .header .line {
        flex-wrap: wrap;
        justify-content: flex-start;
        height: auto;
        padding: 15px
    }

    .header .logo {
        order: 1;
        margin: 0 auto 0 0;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%
    }

    .header .cart-toggle {
        order: 2;
        margin-left: 0;
        margin-top: 2px;
        margin-right: 10px
    }

    .header .header-search-wrapper {
        order: 3;
        width: 100%;
        display: flex;
        margin: 15px 0 0
    }

    .header .logo img,.header .logo svg {
        max-width: 100%;
        transition: .3s ease-out;
        max-height: var(--height_logo_mobile)
    }

    .header .account {
        order: 1;
        margin-right: 10px;
        padding: 0
    }

    .catalog-header .catalog-info .catalogo-form-filtros,.nav,.pagination .page.first .text,.pagination .page.last .text,.product .product-price .product-message .notify-me .icon,.product-wrapper .product-gallery .product-images img.zoomImg {
        display: none
    }

    .banner-home .item img {
        max-height: 620px
    }

    .banner-line.mobile,.page-contact .cols,.page-extra form[action*="afiliados.php?loja="] table td,.pagination .page.first .icons,.pagination .page.last .icons {
        display: block
    }

    .product .product-info {
        padding: 10px 10px 0
    }

    .product .product-info .product-name {
        font-size: 14px;
        line-height: normal
    }

    .product .product-price .product-message {
        font-size: .875rem
    }

    .product .product-price .price.display-cash span.old-price {
        font-size: 12px;
        margin-right: 5px
    }

    .product .product-price .product-message .notify-me {
        font-size: .75rem
    }

    .product .actions {
        position: static;
        padding: 0;
        opacity: 1;
        visibility: visible
    }

    .list-product {
        padding: 0 5px
    }

    .list-product .item {
        margin-bottom: 0
    }

    .footer .footer-main .newsletter .form>*,.noticias,.product-wrapper .product-gallery .product-thumbs .thumbs-list {
        margin: 0
    }

    .box-noticia #noticia_dados {
        padding: 10px
    }

    .section-avaliacoes {
        padding: 0 0 15px
    }

    .pagination .page.first a,.pagination .page.last a {
        padding: 0
    }

    .pagination .page .icon {
        font-size: .625rem;
        width: .5rem;
        display: inline-block
    }

    .pagination .page.first .icons .icon:last-child {
        margin-right: .1875rem
    }

    .catalog-cols .sidebar-category {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 90%;
        padding: 1.875rem .625rem;
        background-color: #fff;
        transform: translateX(calc(-100% - 10px));
        transition: .3s ease-out;
        z-index: 20;
        max-width: 400px
    }

    .catalog-cols .sidebar-category.show,.catalog-header .sort-mobile .sort-panel.show {
        transform: translateX(0)
    }

    .catalog-cols .sidebar-category .sidebar-mobile-header .close-box,.catalog-header .sort-mobile .sort-panel .close-box {
        top: 35px;
        right: 10px
    }

    .catalog-cols .sidebar-category .sidebar-mobile-header .block-title,.catalog-header .sort-mobile .sort-panel .block-title {
        font-size: 1.125rem;
        font-weight: 500;
        color: var(--color_font_dark);
        margin-bottom: 1.875rem;
        padding: 0 3.125rem 1.25rem 0;
        border-bottom: 2px solid var(--color_gray_dark)
    }

    .catalog-cols .sidebar-category .sidebar-mobile-header .block-title .icon,.catalog-header .sort-mobile .sort-panel .block-title .icon {
        font-size: 1.75rem;
        height: 2rem;
        margin-right: .9375rem;
        color: var(--color_primary_medium)
    }

    .catalog-cols .sidebar-category .smart-filter {
        background-color: transparent;
        padding: 0;
        overflow: auto;
        height: calc(100% - 80px);
        scrollbar-color: var(--color_primary_medium) var(--color_gray_medium);
        scrollbar-width: thin;
        border: 0
    }

    .catalog-cols .sidebar-category .smart-filter::-webkit-scrollbar {
        width: 6px;
        background: var(--color_gray_medium)
    }

    .catalog-cols .sidebar-category .smart-filter::-webkit-scrollbar-track {
        background: var(--color_gray_medium)
    }

    .catalog-cols .sidebar-category .smart-filter::-webkit-scrollbar-thumb {
        background: var(--color_primary_medium);
        border-radius: 5px
    }

    .catalog-cols .sidebar-category .smart-filter .filters {
        overflow: hidden
    }

    .catalog-header .catalog-info .catalog-name {
        margin-top: -.625rem;
        font-size: 1.25rem;
        order: 2;
        text-align: center;
        max-width: calc(100% - 170px)
    }

    .catalog-header .catalog-info .catalog-name:after {
        width: 100%;
        left: 50%;
        transform: translateX(-50%)
    }

    .catalog-header .catalog-info .system-filter {
        order: 3
    }

    .catalog-header .catalog-info .button-filter-mobile,.catalog-header .sort-mobile .sort-mobile-button {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex-shrink: 0;
        flex-grow: 0;
        align-items: center;
        width: 70px;
        height: 70px;
        font-size: .75rem;
        text-align: center;
        padding: .625rem .5rem .3125rem;
        border: 1px solid var(--color_gray_medium);
        color: var(--color_primary_medium);
        border-radius: var(--border_radius_images);
        cursor: pointer
    }

    .catalog-header .catalog-info .button-filter-mobile .icon,.catalog-header .sort-mobile .sort-mobile-button .icon {
        display: block;
        font-size: 1.25rem
    }

    .catalog-header .catalog-info .button-filter-mobile {
        margin-right: .9375rem
    }

    .catalog-header .sort-mobile .sort-mobile-button {
        margin-left: .9375rem
    }

    .catalog-header .sort-mobile .sort-panel {
        position: fixed;
        top: 0;
        right: 0;
        width: 100%;
        height: 90%;
        max-width: 400px;
        padding: 1.875rem .625rem;
        background-color: #fff;
        transform: translateX(calc(100% - 0));
        transition: .3s ease-out;
        z-index: 20
    }

    .catalog-header .sort-mobile .sort-panel .sort-options li {
        position: relative;
        padding: 10px 0;
        cursor: pointer;
        transition: .2s ease-out
    }

    .catalog-header .sort-mobile .sort-panel .sort-options li:hover {
        color: var(--color_primary_medium)
    }

    .catalog-header .sort-mobile .sort-panel .sort-options li.active {
        color: var(--color_primary_medium);
        font-weight: 500
    }

    .catalog-header .sort-mobile .sort-panel .sort-options li.active:after {
        position: absolute;
        content: "";
        top: 13px;
        right: 10px;
        display: block;
        width: 1rem;
        height: 1rem;
        background: var(--color_primary_medium) url("data:image/svg+xml;charset=utf-8,%3Csvgxmlns='http://www.w3.org/2000/svg'width='8'height='8'%3E%3Cpathfill='%23fff'd='M6.564.75l-3.593.612-1.538-1.55L04.26l2.9742.99L82.193z'/%3E%3C/svg%3E") no-repeat 50% 50%;
        border-radius: 50%
    }

    .product-wrapper {
        margin-bottom: 2.5rem
    }

    .product-wrapper .product-box .product-gallery {
        width: 100%;
        padding: 0;
        max-width: 500px;
        margin: 0 auto
    }

    .product-wrapper .product-box .product-video .text {
        width: 70px
    }

    .product-wrapper .product-box .product-form {
        width: 100%;
        max-width: 500px;
        margin: 10px auto 0;
        padding: 0
    }

    .product-wrapper .product-form .product-tags .tag {
        width: 40%;
        max-width: 160px
    }

    .compreJunto form,.page-login .page-content .board {
        flex-direction: column
    }

    .compreJunto form .fotosCompreJunto {
        width: 100%;
        padding: 0 0 20px;
        overflow: auto;
        flex-wrap: nowrap;
        justify-content: space-between
    }

    .compreJunto .produto {
        min-width: 42%
    }

    .compreJunto .fotosCompreJunto .plus {
        width: auto;
        transform: none;
        text-align: center;
        line-height: .8;
        justify-content: center;
        margin: 0
    }

    .products-history #produtos,.products-history .visitados_produtos {
        flex-direction: column;
        align-items: center
    }

    .products-history #produtos {
        width: 100%;
        margin: 0
    }

    .paginacao_ajax {
        text-align: center;
        margin-top: 30px
    }

    .page-busca_noticias h1,.page-contact h1,.page-depoimentos .board h1,.page-newsletter .board h1 {
        font-size: 1.25rem
    }

    .page-noticia .board .dataNoticia+h2 {
        font-size: 1.375rem
    }

    .page-noticia .board img {
        display: block;
        margin: 0 auto 15px;
        float: none
    }

    .editDep>li {
        width: 100%;
        padding: 1.25rem
    }

    .editDep ul li.dep {
        text-align: justify
    }

    .editDep h3 img {
        opacity: 1
    }

    #lista_criar td {
        display: block;
        padding: 5px 0
    }

    .caixa-cadastro,.caixa-login {
        max-width: 100%;
        margin: 20px 0
    }

    .page-contact h1+p.description {
        font-size: 1rem
    }

    .page-contact .cols .box-form {
        width: 100%;
        padding: 0
    }

    .page-contact .cols .info-form {
        width: 100%;
        margin-top: 2.5rem;
        padding-top: 1.875rem;
        padding-left: 0;
        border-top: 1px solid var(--color_gray_dark)
    }

    .page-contact .board .botao-commerce {
        max-width: 100%
    }

    .footer .footer-main .logo {
        margin-bottom: 3.125rem
    }

    .footer .footer-main .newsletter {
        flex-direction: column;
        max-width: 100%;
        padding: 15px 0 20px
    }

    .footer .footer-main .newsletter .info {
        width: 100%;
        border: 0
    }

    .footer .footer-main .newsletter .form {
        width: 100%;
        margin-top: 15px;
        padding: 0;
        gap: 8px 0;
        flex-wrap: wrap;
        justify-content: space-between
    }

    .footer .footer-main .newsletter .text .first {
        line-height: 1
    }

    .footer .footer-main .newsletter .text .last {
        font-size: 18px;
        line-height: normal
    }

    .footer .box {
        margin-bottom: 30px;
        width: 100%;
        text-align: left;
        padding: 15px
    }

    .footer .logo-box,.page-newsletter .page-content.success-message-newsletter .board p {
        text-align: center
    }

    .footer .logo-box {
        margin: 0 auto 35px
    }

    .footer .social-media .icon {
        margin: 0 9px
    }

    .footer .list a {
        padding: 6px 0
    }

    .compare-buttons {
        margin: 5px 0
    }

    #listaNoticiaProdutos li {
        width: 50%
    }
}

.cart,.header-wrapper,.quick-buy {
    opacity: 0;
    visibility: hidden
}

@media(max-width: 480px) {
    .page-register .page-content fieldset {
        border:0;
        padding: 0
    }

    #listaNoticiaProdutos li {
        width: 100%
    }
}

form .busca input[type=text] {
    border: 1px solid var(--color_gray_dark);
    line-height: 35px;
    padding: 0 5px;
    margin-right: 10px
}

.is-custom-page .page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--color_font_dark);
    margin-bottom: 30px
}

.account .account-text,header .contact .contact-text {
    color: var(--color_header_text);
    cursor: pointer;
    width: auto
}

.page-newsletter .page-content {
    opacity: 0;
    visibility: hidden;
    transition: .2s ease-out
}

.action-footer-fixed__anchor.active,.quick-buy.active {
    visibility: visible;
    opacity: 1
}

.page-newsletter .formulario-newsletter .box-captcha input,.page-newsletter .formulario-newsletter .box-captcha-newsletter input {
    height: 36px;
    min-width: 200px;
    margin-left: 10px;
    padding: 0 15px
}

.page-newsletter .formulario-newsletter .botao-commerce {
    text-transform: uppercase;
    font-weight: 600;
    margin-top: 1rem;
    display: block
}

.page-newsletter .page-content.success-message-newsletter .board p:first-child {
    max-width: 600px;
    margin: 0 auto;
    text-align: justify
}

.page-newsletter .page-content.success-message-newsletter .board p:first-child a {
    display: block;
    margin: 30px auto 0;
    width: 225px;
    text-align: center
}

@media(max-width: 575px) {
    .section-showcase .container {
        padding:0 .3125rem
    }

    .is-custom-page .page-title,.old-browser .old-browser-title {
        font-size: 1.25rem
    }

    .button-compare,.old-browser .old-browser-options,.page-busca_noticias .noticias li,.page-newsletter .formulario-newsletter .botao-commerce,.page-newsletter .page-content.success-message-newsletter .board p:first-child a {
        width: 100%
    }

    .old-browser .old-browser-options li {
        width: 50%;
        margin: 0!important
    }

    .old-browser .old-browser-options li:nth-child(n+3) {
        margin: 30px 0 0!important
    }

    .modal-theme .modal-info,.modal-theme .modal-wrapper {
        padding: 1.25rem
    }

    .section-header .title-section {
        font-size: 21px
    }

    .section-header .subtitle-section {
        font-size: .875rem
    }

    .product .image {
        position: relative
    }

    .product-wrapper .product-form .product-progressive-discount .tooltip {
        display: none
    }

    .page-newsletter .formulario-newsletter .box-captcha input {
        width: calc(100% - 115px)
    }
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    input,input:focus,select,select:focus,textarea,textarea:focus {
        font-size:16px
    }
}

.banners-grid .container.flex.f-wrap.justify-between {
    flex-wrap: nowrap;
    gap: 20px
}

.banners-grid>.container.flex {
    gap: 20px
}

.block-custom .block-custom__content .item {
    padding: 0 20px
}

.block-custom .block-custom__content .item .icon {
    max-width: 32px;
    max-height: 32px;
    margin-bottom: 10px
}

.block-custom .block-custom__content .item .text {
    line-height: normal;
    padding: 0;
    align-items: center;
    text-align: center;
    font-size: 13px
}

.header .cart-toggle .icon svg {
    width: 22px;
    height: 31px
}

.account,.contact {
    position: relative;
    padding: 8px 0;
    height: 52px;
    width: auto;
    margin: 0 20px 0 0
}

.header-wrapper {
    position: absolute;
    top: 50px;
    width: 260px;
    right: 0;
    background: #fff;
    padding: 20px;
    border: 1px solid #eee;
    border-top: 3px solid var(--color_secondary_medium);
    z-index: 4;
    transition: .3s;
    pointer-events: none
}

.footer .box.box-infos li.hour,.footer-main {
    border-top: 1px solid var(--color_footer_border)
}

header .contact .contact-text {
    font-size: 12px;
    padding-left: 7px;
    margin-top: -3px;
    font-weight: 400;
    line-height: normal
}

.account .account-text {
    margin-top: -4px
}

header .contact.gift>a i svg {
    width: 31px;
    height: 35px
}

.header-wrapper .header-wrapper__item a {
    transition: .3s;
    text-align: left
}

.header-wrapper .header-wrapper__item i {
    color: var(--color_secondary_medium);
    margin-right: 4px;
    font-size: 18px
}

.header-wrapper .header-wrapper__item.header-wrapper__item--contact {
    margin: 8px 0 15px
}

.header-wrapper .header-wrapper__item a:hover {
    opacity: .8
}

.header-wrapper .header-wrapper__content .header-wrapper__item {
    padding: 14px 0;
    border-bottom: 1px solid var(--color_gray_medium)
}

.header-wrapper .header-wrapper__content .header-wrapper__item.header-wrapper__item--whatsapp {
    padding-top: 0
}

.header-wrapper .header-wrapper__content {
    width: 100%;
    color: var(--color_font_medium)
}

header .account:hover .header-wrapper,header .contact:hover .header-wrapper {
    opacity: 1;
    visibility: visible;
    pointer-events: auto
}

.header-wrapper .header-wrapper__item.header-wrapper__item--truck {
    border-bottom: 0!important;
    padding-bottom: 0!important
}

.header-wrapper .header-wrapper__item.header-wrapper__item--truck .header-wrapper__text {
    display: flex;
    align-items: center;
    margin-bottom: 3px
}

.header-wrapper .header-wrapper__item.header-wrapper__item--truck .header-wrapper__text .icon-truck {
    margin-top: 2px;
    text-shadow: 0 0 var(--color_secondary_medium);
    margin-right: 5px
}

.header-wrapper .header-wrapper__item.header-wrapper__item--truck .header-wrapper__form {
    display: flex;
    align-items: center;
    position: relative
}

.header-wrapper .header-wrapper__item.header-wrapper__item--truck .header-wrapper__form input[type=text] {
    height: 40px;
    width: 100%;
    background: #f3f3f3;
    border: 1px solid var(--color_gray_medium);
    font-size: 13px;
    padding-left: 10px;
    padding-right: 30px;
    border-radius: var(--border_radius_buttons)
}

.header-wrapper .header-wrapper__item.header-wrapper__item--truck .header-wrapper__form .header-wrapper__button {
    position: absolute;
    right: 0;
    top: 2px;
    padding: 8px 10px 4px 9px;
    cursor: pointer
}

.header-wrapper .header-wrapper__item.header-wrapper__item--truck .header-wrapper__form .header-wrapper__button i {
    font-size: 15px;
    color: var(--color_secondary_medium);
    margin: 0
}

.footer-main {
    background: var(--color_newsletter_bg);
    border-bottom: 1px solid var(--color_footer_border)
}

.footer .copy .tray {
    display: flex;
    align-items: center;
    margin-top: 0
}

.footer .copy .tray .credits-partner span {
    font-size: 14px;
    display: flex;
    align-items: center
}

.footer .copy .tray .credits-partner span img {
    width: 127px;
    margin-left: 11px
}

#NavLogoTray a:after {
    content: "";
    width: 58px;
    height: 36px;
    background-size: 54px!important;
    margin-left: 8px
}

.footer .box.box-infos li.hour svg {
    width: 23px;
    margin: -3px 6px 0 1px;
    fill: currentColor
}

.footer .box.box-infos li.hour {
    font-size: .87rem;
    margin-top: 7px;
    padding-top: 13px
}

.footer .box.box-infos li.hour .text {
    padding: 7px 0 0 3px
}

.header-wrapper .header-wrapper__content .login-links .login-links__featured i {
    color: #fff;
    margin-right: 0;
    margin-left: -10px
}

.header-wrapper .header-wrapper__content>div span {
    font-size: 14px;
    line-height: normal;
    font-weight: 400;
    display: flex;
    align-items: center;
    margin-bottom: 3px
}

.header-wrapper .header-wrapper__content>div>span {
    margin-bottom: 10px
}

.header-wrapper .header-wrapper__content>div>span>span {
    font-weight: 600
}

.account .account-text .login-links a {
    font-size: 14px;
    font-weight: 700;
    margin-top: -2px
}

.product__buy {
    text-align: center;
    padding: 0 15px;
    z-index: 99;
    width: 100%;
    margin: 0 auto
}

.product__buy .product__buy__fields.product__buy__fields--opened .product__form {
    margin-top: 10px
}

.product__buy .product__buy__fields .product__form {
    display: flex;
    align-items: center;
    margin-top: 0
}

.cart button.dropdown__toggle.cart__toggle,.product__buy .product__buy__fields .product__form button svg,.product__buy .product__buy__fields .visuallyhidden {
    display: none
}

.product__buy .product__buy__fields .product__form .product__quantity {
    padding: 0;
    text-align: center;
    height: 40px;
    margin-right: 0;
    background: #fff;
    width: 100%
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .product-has-variants,.first-level.categoria-offer .buy-whatsapp a,.page-navegacao_visitados .vitrineVisitados .preco-avista:after,.page-navegacao_visitados .vitrineVisitados .preco-avista:before {
    font-size: 12px
}

.actions .product-button,.product__buy .product__buy__fields .product__form button.bt {
    width: 100%;
    padding: 12px 0;
    height: 40px;
    background: var(--color_button_buy_bg);
    color: var(--color_button_buy_text);
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font_family);
    border-radius: var(--border_radius_buttons);
    transition: .3s
}

.product__buy .product__buy__fields {
    text-align: center;
    padding: 0
}

.product__buy .product__buy__fields .cor_variacao {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
    margin-bottom: 10px
}

.product__buy .product__buy__fields .cor_variacao ul {
    display: flex;
    justify-content: center;
    margin-top: 5px;
    width: 100%
}

.product__buy .product__buy__fields .cor_variacao ul li {
    margin-right: 5px;
    margin-bottom: 4px;
    display: flex;
    align-items: center
}

.product__buy .product__buy__fields .cor_variacao .product__color {
    margin-left: 5px
}

.product__buy .select {
    width: 100%;
    margin-top: 7px;
    color: #000;
    background-color: #f1f1f1;
    height: 45px
}

.product__buy .product__buy__fields .cor_variacao ul li img {
    border-radius: 50px;
    border: 1px solid #e1e1e1;
    padding: 3px;
    box-sizing: border-box;
    width: 35px;
    height: 35px
}

.product__buy .product__buy__fields .cor_variacao ul li .cor_selecionada {
    border: 1px solid #717171
}

.product__buy .product__buy__fields.product__buy__fields--opened {
    position: absolute;
    background: #fff;
    left: 0;
    bottom: 0;
    border-top: 1px solid #eee;
    width: 100%;
    box-shadow: 0 0 25px 0 rgb(0 0 0 / 13%);
    padding: 15px
}

.cart {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999999;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    transition: .3s;
    background: rgb(0 0 0 / 39%)
}

.cart .cart-backdrop,.cart__remove {
    right: 0;
    background: 0;
    position: absolute
}

.cart .cart-backdrop {
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 9
}

section#cart {
    width: 430px;
    background: #fff;
    height: 100%;
    padding: 15px 20px;
    transition: .3s;
    position: relative;
    z-index: 10
}

.cart__vue {
    height: 100%;
    display: flex;
    flex-direction: column
}

.cart__content {
    overflow: visible
}

.cart__footer {
    display: flex;
    flex-flow: row;
    align-items: flex-end;
    justify-content: space-between;
    border-top: 1px solid #ddd;
    padding-top: 15px
}

.cart__empty {
    margin: 1.07143rem auto;
    text-align: center;
    color: var(--texto)
}

.cart__remove {
    width: 30px;
    height: 30px;
    bottom: 10px;
    color: red;
    border: 1px solid #eaeaea;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px
}

.cart__product:not(:last-child),.cart__vue__header {
    border-bottom: 1px solid #ddd
}

.cart__remove:hover {
    background-color: var(--texto-cinza)
}

.cart__product {
    display: flex;
    padding: 17px 0;
    display: flex;
    flex-flow: row;
    justify-content: space-between
}

.cart__product__image-container {
    overflow: hidden;
    border-radius: .71429rem;
    width: 85px;
    height: auto;
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center
}

.cart__product__image {
    display: block;
    width: 100%;
    height: auto
}

.cart__product__no-photo {
    font-size: 2.14286rem;
    fill: #e5e5e5
}

.cart__product__details {
    margin-left: 1.07143rem;
    flex: 1 1;
    display: flex;
    flex-flow: row wrap;
    justify-content: space-between;
    align-items: flex-start;
    position: relative
}

.cart__product__name {
    flex: 1 1;
    margin-right: .71429rem;
    margin-bottom: .35714rem;
    line-height: 1.1;
    font-size: 14px
}

.brands-custom .brands-custom__content .item a:hover,.buy-sizes .container .buy-sizes__content .item:hover,.cart .cart__user-actions div a:hover,.cart__product__name:hover,.section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header a:hover {
    opacity: .7
}

.cart__product__qtd {
    background: var(--color_primary);
    color: #fff;
    border-radius: 50px;
    width: 20px;
    height: 20px;
    justify-content: center;
    display: flex;
    align-items: center;
    font-size: 13px
}

.cart__product__price {
    color: var(-texto);
    font-weight:700;font-size:17px;width:100%}

.cart__product__list {
    display: block;
    width: 100%;
    margin-right: .71429rem;
    color: var(--texto-cinza)
}

.cart__product__item {
    font-size: .85714rem
}

.cart__product__item:before {
    content: "-";
    margin-right: .25em
}

.cart__total-price {
    color: #333
}

.cart__total-price__value {
    color: var(--price-color);
    font-size: 19px;
    font-weight: 700;
    display: block;
    line-height: 1.2
}

.cart__footer,.product__buy .product__buy__fields .cor_variacao ul {
    flex-wrap: wrap
}

.cart__options {
    width: 100%;
    margin-bottom: 15px;
    display: none
}

.cart__footer .cart__options.active,.page-navegacao_visitados .vitrineVisitados>li:hover>a.Foto:after,.page-navegacao_visitados:not(:has(.vitrineVisitados .dados)) div#Vitrine,.quick-buy span#info_preco {
    display: block
}

.cart__footer .cart__bt.buy--continue {
    margin-top: 5px;
    background: 0;
    font-size: 11px;
    border: 1px solid var(--color_primary);
    color: var(--color_primary)
}

.buy-whatsapp a,.cart .cart__bt {
    border-radius: var(--border_radius_buttons);
    font-size: 13px;
    text-align: center
}

.cart .cart__bt {
    margin: 0 0 0 auto;
    font-weight: 600;
    width: 200px;
    padding: 13px 5px;
    text-transform: uppercase;
    background: var(--color_button_buy_bg);
    color: var(--color_button_buy_text)
}

.cart .cart__remove-all {
    width: 100%;
    padding: 10px;
    font-size: 13px;
    font-weight: 700;
    background: red;
    color: #fff
}

.cart .dropdown__content {
    flex-grow: 1;
    overflow: auto;
    padding-right: 20px;
    margin: 8px 0
}

.cart section#cart {
    transform: translateX(105%)
}

.cart__vue__header {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px
}

.cart__vue__header .cart__vue__icon {
    display: flex;
    align-items: center;
    cursor: pointer
}

.cart__vue__header .cart__vue__icon .icon {
    margin-right: 10px
}

.cart__vue__header .cart__vue__text span {
    font-weight: 700
}

.product .product-price .product-message span.seal-not-stock {
    position: absolute;
    right: 8px;
    top: 9px;
    font-size: 12px;
    padding: 4px 20px;
    background: red;
    color: #fff;
    border-radius: var(--border_radius_buttons)
}

.product-wrapper .product-box div#produto_nao_disp>#nao_disp {
    margin-bottom: 15px;
    z-index: 1;
    position: relative;
    background: #fff
}

.product-wrapper .product-box div#produto_nao_disp>#nao_disp>div:first-child {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0
}

.buy-sizes__image,.fixed-whatsapp {
    align-items: center;
    box-sizing: border-box
}

.nav .category-image img {
    max-width: 20px;
    max-height: 20px
}

.product__buy .product__buy__fields .product__form button.product__close-fast-shopping {
    position: absolute;
    right: 0;
    top: 0;
    width: 35px;
    height: 35px;
    background: 0
}

.brands-custom:not(.active-slide) .next,.brands-custom:not(.active-slide) .prev,.message-top .next,.message-top .prev,.not-slide-drag .dots,.not-slide-drag .swiper-button-disabled,.product-tabs.short-tabs .tabs-content .payment-tab .option-details table td:last-child,.product-tabs.short-tabs .tabs-content .payment-tab .option-details table th:last-child,.product-tabs.short-tabs .tabs-nav,.product__buy .product__buy__fields .product__form button.product__close-fast-shopping svg {
    display: none
}

.product__buy .product__buy__fields .product__form button.product__close-fast-shopping:after,.product__buy .product__buy__fields .product__form button.product__close-fast-shopping:before {
    content: "";
    width: 20px;
    height: 2px;
    background: #000;
    position: absolute;
    right: 10px;
    border-radius: 50px;
    transform: rotate(45deg);
    top: 17px
}

.product__buy .product__buy__fields .product__form button.product__close-fast-shopping:before {
    transform: rotate(-45deg)
}

.message-top {
    text-align: center;
    padding: 0;
    font-size: 14px;
    background: var(--message_color_bg,#000);
    color: var(--message_color_txt,#fff);
    border-bottom: 1px solid var(--color_header_details)
}

.message-top .item a {
    display: table;
    text-align: center;
    margin: 10px auto;
    color: currentColor;
    font-weight: 400
}

.block-custom .block-custom__content .item .text strong {
    font-weight: 700;
    font-size: 15px;
    font-family: var(--font_family_title)
}

.buy-sizes .container .buy-sizes__content a:hover {
    background: #c5c5c5
}

.buy-sizes .container .buy-sizes__content .item img {
    max-width: 100%;
    border-radius: 5px;
    display: block;
    object-fit: cover;
    width: 100%;
    height: 100%
}

.buy-sizes .container .buy-sizes__content .item .buy-sizes__name {
    text-align: center;
    border-radius: 4px;
    width: auto;
    margin: 8px auto 0;
    display: table;
    padding: 5px 10px;
    position: relative;
    font-size: 14px;
    font-weight: 700;
    transition: .2s ease-out
}

.buy-sizes__image {
    display: flex;
    justify-content: center;
    width: 170px;
    height: 170px;
    border-radius: 10em;
    overflow: hidden;
    border: 1px solid rgba(221,221,221,.54);
    margin: 0 auto
}

.fixed-whatsapp svg.whatsapp {
    position: relative;
    top: 0;
    left: 0;
    padding: 0;
    width: 30px;
    height: 30px
}

.whatsapp path:first-child {
    fill: #13b918
}

.fixed-whatsapp {
    width: 50px;
    right: 15px!important;
    background-color: #13b918;
    box-shadow: 0 0 0 0 #13b918;
    -webkit-animation: 4.25s cubic-bezier(.66,0,0,1) infinite pulse;
    -moz-animation: 4.25s cubic-bezier(.66,0,0,1) infinite pulse;
    -ms-animation: pulse 4.25s infinite cubic-bezier(.66,0,0,1);
    animation: 4.25s cubic-bezier(.66,0,0,1) infinite pulse;
    text-align: center;
    padding: 10px;
    height: 50px;
    position: fixed;
    z-index: 99998;
    display: flex;
    justify-content: center;
    bottom: 18px;
    border-radius: 50px;
    transition: bottom .5s
}

.fixed-whatsapp:hover {
    -webkit-animation: none;
    -moz-animation: none;
    -ms-animation: none;
    animation: none
}

@-webkit-keyframes pulse {
    to {
        box-shadow: 0 0 0 15px rgba(232,76,61,0)
    }
}

@-moz-keyframes pulse {
    to {
        box-shadow: 0 0 0 15px rgba(232,76,61,0)
    }
}

@-ms-keyframes pulse {
    to {
        box-shadow: 0 0 0 15px rgba(232,76,61,0)
    }
}

@keyframes pulse {
    to {
        box-shadow: 0 0 0 15px rgba(232,76,61,0)
    }
}

.brands-custom .brands-custom__content .item img {
    max-width: 100%;
    max-height: 80px
}

.brands-custom .brands-custom__content .item .brands-custom__image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 90px
}

.brands-custom .brands-custom__content .item a {
    transition: .3s
}

.slide-catalog.swiper-container .swiper-wrapper {
    max-width: 100%;
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between
}

.slide-catalog.swiper-container .swiper-wrapper .item.swiper-slide {
    width: calc(100% / 6);
    padding: 0 10px
}

.slide-catalog.swiper-container .swiper-wrapper .item.swiper-slide img.swiper-lazy {
    opacity: 1;
    max-width: 100%
}

.banner .slide-catalog.swiper-container {
    margin-top: 15px;
    margin-bottom: 30px
}

html.page-acompanhe-seu-pedido .page-content .board_htm.description img {
    max-width: 100%!important
}

.footer .box-infos a .icon {
    margin-right: 4px
}

.section-showcase .swiper-carousel .prev,.section-showcase .swiper-container .prev {
    left: 5px
}

.section-showcase .swiper-carousel .next,.section-showcase .swiper-container .next {
    right: 5px
}

.footer .cols .container>.box.payments-seals>.overflow {
    width: auto
}

.footer .copy .mode-preview {
    margin-top: 25px;
    margin-bottom: 30px
}

.product__buy .product__buy__fields .product__form button.bt:hover {
    opacity: .8
}

.template-instagram div#instafeed a img {
    max-width: 100%;
    object-fit: cover;
    width: 100%;
    transition: .3s;
    position: absolute;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: var(--border_radius_images)
}

.template-instagram div#instafeed a:hover img {
    filter: brightness(.8)
}

.section-header .title-section:after {
    content: "";
    display: block;
    margin: 10px auto 0;
    width: 40px;
    height: 4px;
    background: var(--color_primary_medium)
}

.footer .cols .container>.box.box-logo {
    display: flex;
    flex-direction: column;
    text-align: center
}

.footer .cols .container>.box.box-logo img {
    max-width: 100%;
    max-height: var(--height_logo_footer)
}

.footer .cols .container>.box.box-logo .box-logo-socials {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px
}

.footer .cols .container>.box.box-logo .box-logo-socials a {
    margin: 0 7px;
    font-size: 20px
}

.not-slide-drag .swiper-wrapper,.template-instagram:not(.active-slide) .swiper-wrapper {
    justify-content: center
}

.brands-custom .brands-custom__content {
    align-items: center
}

.template-instagram div#instafeed a:after {
    content: "";
    padding-bottom: 100%;
    display: block
}

.footer .copy .tray .credits-partner a {
    filter: grayscale(1);
    transition: .3s
}

.footer .cols .container>.box.payments-seals .overflow:nth-child(2) {
    padding-top: 25px
}

.product__buy .product__buy__fields .product__form .label-quantity {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    overflow: hidden;
    width: 110px;
    position: relative;
    margin-right: 6px
}

.product__buy .product__buy__fields .product__form .label-quantity button {
    width: 20px;
    height: 30px;
    background: 0;
    font-size: 15px;
    position: absolute;
    top: calc(50% - 15px)
}

.product__buy .product__buy__fields .product__form .label-quantity button.btn--plus:after,.product__buy .product__buy__fields .product__form .label-quantity button:before {
    content: "";
    width: 9px;
    height: 1px;
    position: absolute;
    right: 5px;
    top: 14px;
    background: var(--color_secondary_medium)
}

.product__buy .product__buy__fields .product__form .label-quantity button.btn--plus:after {
    transform: rotate(90deg)
}

.product__buy .product__buy__fields .product__form .product__quantity::-webkit-inner-spin-button,.product__buy .product__buy__fields .product__form .product__quantity::-webkit-outer-spin-button {
    -webkit-appearance: auto;
    -webkit-appearance: none;
    margin: 0
}

.product__buy .product__buy__fields .product__form .product__quantity[type=number] {
    -moz-appearance: textfield
}

.product__buy .product__buy__fields .product__form .label-quantity button.btn.btn--minus {
    left: 2px
}

.product__buy .product__buy__fields .product__form .label-quantity button.btn.btn--plus {
    right: 2px
}

.section-notices .noticias {
    display: flex;
    flex-wrap: nowrap;
    gap: 20px
}

.box-noticia #noticia_dados h3:after {
    content: "";
    display: block;
    margin: 10px 0;
    width: 40px;
    height: 4px;
    background: var(--color_primary_medium)
}

.section-notices h2.title-section>a {
    position: absolute;
    right: 0;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: -1px
}

.section-notices h2.title-section>a i {
    margin-left: 5px;
    color: var(--color_primary_medium);
    margin-top: -1px
}

.section-notices {
    padding: 10px 0
}

.nav .list .second-level li {
    padding: 2px 0
}

.buy-whatsapp a {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px;
    background: var(--button_whats_color_bg);
    color: var(--button_whats_color_txt);
    border: 1px solid var(--button_whats_color_border);
    transition: .3s
}

.buy-whatsapp a .icon.icon-whatsapp {
    margin-right: 5px
}

.buy-whatsapp {
    padding: 0 15px;
    margin-top: 5px
}

.buy-whatsapp a:hover {
    background: var(--button_whats_color_hover_bg);
    color: var(--button_whats_color_hover_txt);
    border-color: var(--button_whats_color_hover_border)
}

.buy-whatsapp.buy-whatsapp--page-product {
    padding: 0;
    margin-top: 10px
}

.buy-whatsapp.buy-whatsapp--page-product a {
    padding: 8px;
    font-size: 15px
}

.product-wrapper .product-tabs.long-tabs .tabs-nav li.tab {
    margin-right: 34px;
    padding: 9px 0
}

.product-tabs.short-tabs .tabs-content .tab-link-mobile {
    display: block;
    position: relative;
    width: 100%;
    padding: 15px 0;
    font-size: 15px;
    font-weight: 500;
    text-decoration: none;
    border-bottom: 1px solid var(--color_gray_dark);
    transition: .2s ease-out;
    text-align: left
}

.product-tabs.short-tabs .tabs-content .tab-link-mobile:first-child {
    border-top: 1px solid var(--color_gray_dark)
}

.product-tabs.short-tabs .tabs-content .tab-link-mobile:after {
    content: "\ea01";
    position: absolute;
    top: 19px;
    right: 0;
    margin-left: .3125rem;
    font-family: go!important;
    font-size: .75rem;
    font-style: normal!important;
    font-weight: 400!important;
    font-variant: normal!important;
    text-transform: none!important;
    transition: .2s ease-out
}

.product-tabs.short-tabs .tabs-content .tab {
    display: none;
    padding: 8px 0
}

.product-tabs.short-tabs .tabs-content .tab.payment-tab {
    padding-top: .625rem;
    padding-bottom: 1.25rem
}

.product-tabs.short-tabs .tabs-content .payment-tab .option-details table td:first-child,.product-tabs.short-tabs .tabs-content .payment-tab .option-details table th:first-child {
    width: 20%
}

.product-tabs.short-tabs .tabs-content .payment-tab .option-details table td:nth-child(2),.product-tabs.short-tabs .tabs-content .payment-tab .option-details table th:nth-child(2) {
    width: 80%
}

.product-tabs.short-tabs .tabs-content .payment-tab .option:last-child a {
    border-bottom: 0
}

.product-tabs.short-tabs .tabs-content .payment-tab .option:last-child.show a {
    border-bottom: 1px solid var(--color_gray_dark)
}

.product-tabs.short-tabs .tabs-content .payment-tab .option:last-child.show .option-details {
    border-bottom: 0;
    padding-bottom: 0
}

.list-product-countdown {
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%
}

.list-product-countdown.list-product-countdown--showcase {
    color: var(--countdown_timer_color_txt);
    pointer-events: none
}

.list-product-countdown.list-product-countdown--showcase:before {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    content: "";
    top: 0;
    background: var(--countdown_timer_color_bg);
    opacity: .8
}

.list-product-countdown .list-product-countdown__timer {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 9px 5px;
    justify-content: space-between;
    max-width: 220px;
    margin: 0 auto;
    position: relative;
    z-index: 1
}

.list-product-countdown .list-product-countdown__timer .item {
    width: auto!important;
    margin: 0;
    padding: 0!important;
    align-items: center
}

.list-product-countdown .list-product-countdown__timer .item i {
    font-style: normal;
    margin-right: 2px;
    font-size: 14px;
    font-weight: 700
}

.list-product-countdown .list-product-countdown__timer li.blank {
    display: none
}

.list-product-countdown .list-product-countdown__timer .item span {
    font-size: 11px;
    margin-bottom: -2px
}

.list-product-countdown.list-product-countdown--single {
    position: relative;
    bottom: 0;
    margin-top: 10px;
    border-radius: var(--border_radius_buttons);
    justify-content: center;
    color: var(--countdown_timer_color_txt);
    background: var(--countdown_timer_color_bg)
}

.list-product-countdown.list-product-countdown--single .list-product-countdown__timer {
    max-width: 100%;
    justify-content: center;
    list-style: none
}

.list-product-countdown.list-product-countdown--single .list-product-countdown__timer .item {
    padding: 3px 10px!important;
    display: flex;
    align-items: center
}

.list-product-countdown.list-product-countdown--single .list-product-countdown__timer .item i {
    font-size: 20px
}

.list-product-countdown.list-product-countdown--single .list-product-countdown__timer .item span {
    font-size: 15px;
    margin: 0 0 -3px
}

.list-product-countdown.list-product-countdown--single .list-product-countdown__timer li.blank {
    display: inline-block;
    margin: 0
}

.section-showcase.section-showcase--countdown {
    background: var(--showcase_countdown_color_bg);
    padding: 30px 0 20px
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px
}

.section-showcase.section-showcase--countdown>.container {
    max-width: 100%;
    padding: 0
}

.section-showcase.section-showcase--countdown .list-product .item .product {
    padding-bottom: 15px
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header p {
    color: var(--showcase_countdown_color_txt)
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .wrapper-header-countdown__timer {
    display: flex;
    align-items: center;
    list-style: none;
    color: var(--showcase_countdown_color_txt_number)
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .wrapper-header-countdown__timer i {
    font-style: normal;
    font-size: 34px;
    font-weight: 700;
    margin-right: 0;
    line-height: normal
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .wrapper-header-countdown__timer .item {
    display: flex;
    align-items: center;
    padding: 0 15px;
    flex-direction: column
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .wrapper-header-countdown__timer .item span {
    font-size: 16px;
    margin-top: -3px
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header {
    width: auto;
    text-align: left;
    margin-bottom: 0
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header .title-section {
    text-align: left;
    line-height: normal;
    color: var(--showcase_countdown_color_txt)
}

.page-navegacao_visitados .Mapa img,.page-navegacao_visitados .bottom.central-bottom,.page-navegacao_visitados .vitrineVisitados .txt-avista,.page-navegacao_visitados .vitrineVisitados .txt-com-desconto,.page-navegacao_visitados .vitrineVisitados span.valores span.oculta_destaque,.page-navegacao_visitados .vitrineVisitados span.valores span.txt-forma-pagamento,.page-navegacao_visitados p.change,.section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header .title-section:after {
    display: none
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header a {
    background: var(--showcase_countdown_color_bg_button);
    color: var(--showcase_countdown_color_txt_button);
    display: table;
    padding: 10px 35px;
    margin-top: 15px;
    border-radius: var(--border_radius_buttons);
    font-size: 14px;
    font-weight: 700;
    transition: .3s
}

.section-showcase.section-showcase--countdown .wrapper-header-countdown .wrapper-header-countdown__timer li.blank {
    font-weight: 700;
    font-size: 22px
}

.footer .copy>.container>.text {
    margin: 5px 0 13px
}

.product-wrapper .product-gallery .product-images img.zoomImg {
    pointer-events: none!important
}

.page-navegacao_visitados .board hr {
    border: 0;
    border-bottom: 1px solid #e5e5e5;
    margin: 0 0 22px;
    display: none
}

.page-navegacao_visitados .vitrineVisitados {
    display: contents
}

.page-navegacao_visitados div#Vitrine {
    position: relative;
    display: grid;
    grid-template: repeat(1,1fr)/repeat(4,1fr);
    gap: 10px;
    text-align: center
}

.page-navegacao_visitados .vitrineVisitados>li {
    width: 100%;
    text-align: center;
    border: 1px solid var(--color_gray_dark);
    border-radius: 5px;
    position: relative;
    padding: 20px 10px 60px;
    transition: .3s;
    background: #fff
}

.page-navegacao_visitados .vitrineVisitados>li>a.Foto:after {
    content: "VISUALIZAR PRODUTO";
    background: var(--color_button_buy_bg);
    border-radius: 4px;
    padding: 12px 10px;
    color: #fff;
    transition: .3s;
    text-transform: uppercase;
    font-size: 13px;
    font-weight: 700;
    position: absolute;
    bottom: 6px;
    width: calc(100% - 12px);
    box-sizing: border-box;
    display: none
}

.page-navegacao_visitados .vitrineVisitados .bts2 {
    position: absolute;
    width: 26px;
    height: 26px;
    background: red;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 700;
    top: 5px;
    right: 5px;
    transition: .3s;
    z-index: 1
}

.page-navegacao_visitados .vitrineVisitados .bts2:hover {
    background: #ce0000
}

.page-navegacao_visitados .vitrineVisitados>li>a.Foto {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 0 20px;
    min-height: 170px
}

.page-navegacao_visitados .vitrineVisitados a.NomeProdLista {
    max-width: 100%;
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    color: var(--cor-preto-principal);
    text-align: center;
    margin: 15px 0 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 36px
}

.page-navegacao_visitados .vitrineVisitados span.valores div#precoDe {
    font-size: 14px;
    text-decoration: line-through;
    font-weight: 400;
    margin: 0;
    display: inline-block;
    text-align: right;
    padding-right: 2px;
    color: #999
}

.page-navegacao_visitados .vitrineVisitados span.valores {
    margin: 10px 0 0;
    line-height: 10px
}

.page-navegacao_visitados .vitrineVisitados .preco-avista {
    order: 1;
    font-weight: 800;
    color: var(--cor-verde-principal);
    font-size: 20px;
    margin: 8px 0 0;
    width: 100%
}

.page-navegacao_visitados .vitrineVisitados .preco-avista:after {
    content: " via pix/boleto"
}

.page-navegacao_visitados .vitrineVisitados .preco-avista:before {
    content: "ou "
}

.page-navegacao_visitados .vitrineVisitados .preco-parc2 strong,.page-navegacao_visitados .vitrineVisitados span.operadora,.page-navegacao_visitados .vitrineVisitados span.preco-de,.page-navegacao_visitados .vitrineVisitados span.txt-corparcelas .preco-parc2:before {
    font-size: 13px;
    text-transform: lowercase
}

.page-navegacao_visitados .vitrineVisitados strong.preco-parc2 {
    display: flex;
    gap: 2px
}

.page-navegacao_visitados .vitrineVisitados span.txt-corparcelas .preco-parc2:before {
    content: "at� "
}

.page-navegacao_visitados .vitrineVisitados span.txt-cadaparcelas,.page-navegacao_visitados .vitrineVisitados strong.color {
    color: var(--cor-azul-principal);
    font-size: 13px
}

.page-navegacao_visitados .vitrineVisitados span.precoAvista:not(.preco-avista) {
    display: inline-block;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
    margin: 0 0 10px
}

.page-navegacao_visitados .vitrineVisitados span.operadora+br+span.txt-corparcelas span.preco-parc2:before {
    content: "ou em "
}

.page-navegacao_visitados .board h1.color:after {
    content: "produtos acessados recentemente";
    display: block;
    font-size: 14px;
    color: var(--cor-preto-3);
    margin: 10px 0 0
}

.page-navegacao_visitados .vitrineVisitados>li>a.Foto:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0
}

.page-navegacao_visitados .vitrineVisitados>li:hover {
    border-color: #c7c7c7
}

.page-navegacao_visitados .board {
    display: table;
    order: -1
}

.page-navegacao_visitados .container2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 25px 0 30px;
    align-items: center
}

.page-navegacao_visitados .Mapa:before {
    content: "Visulizar mapa do site";
    padding: 12px 18px;
    background: var(--cor-azul-principal);
    border-radius: 5px;
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    transition: .3s;
    display: none
}

.page-navegacao_visitados .Mapa:hover:before {
    background: var(--cor-azul-4)
}

.page-navegacao_visitados:not(:has(.vitrineVisitados .dados)) ul.vitrineVisitados:before {
    content: "Nenhum produto acessado recentemente!";
    display: block;
    font-size: 20px;
    color: var(--cor-azul-principal);
    font-weight: 600;
    padding: 30px 0;
    background: #fff;
    border-radius: 5px;
    border: 1px solid #e5e5e5;
    margin: 0 0 11px
}

.page-navegacao_visitados .vitrineVisitados:first-child>li:not(:has(.dados)) {
    display: none!important;
    padding: 0!important;
    border: none!important;
    text-align: left
}

.product-wrapper .product-form .product-tags .tag span {
    background: var(--color_secondary_medium);
    padding: 5px 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center
}

.product .product-tags .tag span {
    display: block;
    width: 100%;
    padding: 3px 5px;
    background: var(--color_secondary_medium);
    border-radius: var(--border_radius_buttons)
}

.block-custom.custom-home-reviews .block-custom__content .item {
    background: #fff;
    padding: 20px;
    align-items: flex-start;
    margin-top: 10px;
    margin-bottom: 10px;
    border: 1px solid var(--color_gray_medium);
    border-radius: var(--border_radius_images);
    height: auto
}

.custom-home-reviews .custom-home-reviews__right p {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-line-clamp: 5
}

.block-custom.custom-home-reviews .block-custom__content .item .custom-home-reviews__left {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-top: 0;
    height: 60px;
    width: 60px;
    margin-right: 14px;
    min-width: 60px
}

.block-custom.custom-home-reviews .block-custom__content .item .custom-home-reviews__left img {
    max-width: initial;
    max-height: initial;
    height: 100%;
    width: 100%;
    object-fit: cover;
    border-radius: 50px
}

.block-custom.custom-home-reviews .block-custom__content .item .custom-home-reviews__right small {
    margin-bottom: 8px
}

.block-custom.custom-home-reviews .block-custom__content .item .custom-home-reviews__right span.custom-home-reviews__stars span {
    width: 12px;
    height: 15px;
    background: url(../img/star.svg) center center/11px no-repeat;
    margin: 0 1px
}

.block-custom.custom-home-reviews .block-custom__content .item .custom-home-reviews__right span.custom-home-reviews__stars {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-left: 10px;
    margin-top: -1px
}

.block-custom.custom-home-reviews .block-custom__content .item .custom-home-reviews__right strong {
    display: flex;
    align-items: center;
    justify-content: flex-start
}

.product-wrapper .product-gallery .product-thumbs.vertical-thumbs.show-arrows .prev {
    transform: rotate(90deg);
    right: initial;
    top: -35px;
    width: 35px;
    height: 35px;
    font-size: 13px;
    left: calc(50% - 17.5px);
    margin: 0;
    bottom: initial
}

.product-wrapper .product-gallery .product-thumbs.vertical-thumbs.show-arrows .next {
    transform: rotate(90deg);
    right: initial;
    bottom: -35px;
    width: 35px;
    height: 35px;
    font-size: 13px;
    left: calc(50% - 17.5px);
    margin: 0;
    top: initial
}

.custom-faq .custom-faq__quest {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f9f9f9;
    padding: 18px 23px;
    cursor: pointer;
    border-radius: var(--border_radius_buttons)
}

.custom-faq .custom-faq__item {
    margin-bottom: 5px
}

.custom-faq .custom-faq__resp {
    padding: 15px;
    display: none;
    font-size: 15px
}

.custom-faq .custom-faq__quest h3 {
    font-weight: 500;
    font-size: 16px;
    padding-right: 25px
}

.custom-faq .custom-faq__quest .icon {
    font-size: 15px;
    transition: .3s
}

.custom-faq .custom-faq__quest.active .icon {
    transform: rotateX(180deg)
}

.box-noticia #noticia_imagem img.loaded,.box-noticia #noticia_imagem img.swiper-lazy-loaded,.filters-list input:checked~.icon-radio:before,.footer .copy .tray .mode-preview:hover,.item-image img.swiper-lazy.swiper-lazy-loaded,.page-busca_noticias .box-noticia #noticia_imagem img,.page-contact .page-content.active,.swiper-container .dots .dot-active:after,img.swiper-lazy-loaded,img.transform.loaded,img.transform.swiper-lazy-loaded {
    opacity: 1
}

.quick-buy {
    position: sticky;
    bottom: -1px;
    width: 100%;
    left: 0;
    z-index: 99;
    background: #fff;
    border-top: 1px solid #eee;
    padding: 15px;
    transition: .5s;
    transform: translateY(110%)
}

.quick-buy .quick-buy__content .quick-buy__image {
    margin-right: 25px;
    display: flex;
    align-items: center;
    justify-content: center
}

.quick-buy .quick-buy__content .quick-buy__image img {
    max-width: 100%;
    max-height: 80px
}

.quick-buy .quick-buy__content .quick-buy__name {
    flex-grow: 1
}

.quick-buy .quick-buy__content .quick-buy__price {
    max-width: 250px;
    margin: 0 30px 0 20px;
    line-height: 18px
}

.modal-theme.product-ruler-modal .modal-info img:not(:last-child),.quick-buy #produto_preco .txt-por,.quick-buy .quick-buy__content .quick-buy__price div#info,.quick-buy .quick-buy__content .quick-buy__price div#preco #economize,.quick-buy .quick-buy__content .quick-buy__price div#preco br,.quick-buy .quick-buy__content .quick-buy__price div#preco div#detalhes_formas,.quick-buy span.txt-forma-pagamento {
    display: none
}

.quick-buy .quick-buy__content .quick-buy__action {
    width: 350px
}

.quick-buy .quick-buy__content .quick-buy__action .page-product__quantity {
    position: relative;
    margin-bottom: 0;
    margin-right: 5px
}

.quick-buy .quick-buy__content .quick-buy__action .page-product__quantity .plus-minus input#plus {
    left: initial;
    right: 8px;
    font-size: 17px;
    top: calc(50% - 9px)
}

.quick-buy .quick-buy__content .quick-buy__action .page-product__quantity .plus-minus input {
    position: absolute;
    width: 20px;
    height: 20px;
    top: calc(50% - 10px);
    left: 8px;
    font-size: 25px;
    line-height: 15px;
    cursor: pointer;
    color: var(--color_secondary_medium);
    background: 0 0!important;
    box-shadow: none!important
}

.quick-buy .quick-buy__content .quick-buy__action button#button-buy,.quick-buy .quick-buy__content .quick-buy__action.quick-top button.to-top {
    height: 45px;
    font-size: 15px;
    border-radius: var(--border_radius_buttons);
    width: 100%
}

.quick-buy .quick-buy__content .quick-buy__action .page-product__quantity input#quant {
    height: 45px;
    text-align: center;
    box-sizing: border-box;
    width: 100px;
    background: 0;
    border-color: var(--color_gray_dark);
    font-size: 17px
}

.quick-buy.active {
    transform: translateY(0)
}

.quick-buy .quick-buy__content .quick-buy__price div#produto_preco {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end
}

.quick-buy .quick-buy__content .quick-buy__action .page-product__quantity-and-buy {
    display: flex;
    align-items: center
}

.newsletter-modal .box-center img,.quick-buy .quick-buy__content .quick-buy__action .page-product__quantity-and-buy .page-product__button-buy {
    width: 100%
}

body:has(.quick-buy.active) .fixed-whatsapp {
    bottom: 130px
}

.quick-buy #produto_preco #precoDe {
    font-size: 13px;
    font-weight: 500;
    text-decoration: line-through;
    text-transform: lowercase;
    color: var(--color_primary_medium);
    margin-right: 5px;
    order: -1
}

.quick-buy #produto_preco .PrecoPrincipal {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 3px;
    color: var(--color_font_price)
}

.quick-buy #produto_preco #info_preco {
    font-size: 13px;
    font-weight: 400;
    line-height: normal;
    width: 100%;
    text-align: right
}

.quick-buy .quick-buy__content .quick-buy__action.quick-top {
    width: 250px
}

.quick-buy .quick-buy__content .quick-buy__action.quick-top button.to-top {
    padding: .625rem 1.25rem;
    font-family: var(--font_family);
    font-weight: 700;
    transition: .2s ease-out;
    text-transform: uppercase;
    background: var(--color_button_buy_bg);
    color: var(--color_button_buy_text)!important
}

.product .image {
    overflow: hidden
}

.footer .cols .container>.box.box-logo .box-logo-socials a.custom-svg {
    font-size: 18px
}

.quick-buy .quick-buy__content .quick-buy__price #preco_atual[value="0.00"]+#preco {
    margin: 0
}

.product .alert-variant-form {
    background: #ffe0e0;
    color: #d96767;
    border: 1px solid #d96767;
    margin-top: 7px;
    font-size: 13px;
    padding: 5px;
    border-radius: var(--border_radius_buttons)
}

.quick-buy .quick-buy__content .quick-buy__price div#produto_preco>span.color-tone-2:first-child {
    font-size: 13px
}

.section-avaliacoes .dots.swiper-pagination-bullets {
    margin-top: 8px;
    display: none
}

body:has(.cart.cart-show) {
    overflow: hidden
}

div#video-home .video-item iframe {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%
}

div#video-home .video-item {
    position: relative;
    padding-bottom: var(--video_home_height);
    display: block
}

.modal-theme .close-icon .icon {
    font-size: 19px
}

.ruler-product {
    cursor: pointer;
    font-size: 14px;
    color: #333;
    font-weight: 500;
    padding: 10px 17px 12px;
    border-radius: 5px;
    width: auto;
    display: none;
    border: 1px solid #eee;
    margin-top: 10px
}

.ruler-product .icon-ruler {
    width: 27px;
    display: inline-block;
    height: 21px;
    vertical-align: bottom
}

.ruler-product .icon-ruler svg {
    width: 22px;
    height: 22px;
    fill: var(--color_primary_medium);
    margin-right: 5px;
    vertical-align: revert
}

.ruler-product.active {
    display: table
}

.product-wrapper .product-form .product-main-info .product-details .product-details__item .dados-valor {
    margin-right: 6px
}

.product-wrapper .product-form .product-main-info .product-details .product-details__item strong {
    font-weight: 400!important
}

.product:has(span.seal-not-stock) .discount {
    display: none
}

footer.footer .banner-home.banner-footer-pages {
    padding-bottom: 25px
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content .brinde_detalhes {
    text-align: center;
    margin-bottom: 45px
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content .brinde_detalhes .variacao img {
    max-height: 200px
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content .brinde_detalhes .variacao [id*=menu] {
    text-align: center;
    margin-bottom: 15px
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content h3 {
    margin-bottom: 20px;
    text-align: center;
    margin-top: 9px
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content .brinde_detalhes .variacao [id*=menu] .varTit {
    margin-bottom: 5px;
    margin-top: 5px
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content .brinde_detalhes .detalhes {
    margin-bottom: 10px
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content .brinde_detalhes .botao:before {
    width: 160px;
    border-radius: var(--border_radius_images)
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content .brinde_detalhes .botao {
    width: 160px
}

.modal:has(.brinde_detalhes .detalhes) .modal-header {
    padding: 0;
    min-height: 0;
    border: 0
}

.modal-theme.modal-video:not(.show) iframe.iframe-lazy {
    visibility: hidden!important
}

.modal:has(.brinde_detalhes .detalhes) .modal-footer {
    padding: 0;
    border: 0
}

.modal:has(.brinde_detalhes .detalhes) div#modal-form-content .brinde_detalhes .variacao [id*=menu] .varCont select {
    height: 35px;
    padding-left: 15px;
    padding-right: 40px!important;
    border-radius: var(--border_radius_images);
    width: 160px
}

.product:has(.product-price .product-message .notify-me) .product-tags,.product:has(.product-price .product-message .notify-me) .product-tags-circle {
    display: none!important
}

.cart.loading-cart .cart__empty p,.progressive-discount-banners:not(:has(*)) {
    display: none
}

body:has(.modal-theme.product-ruler-modal.show) {
    overflow: hidden
}

.modal-theme.product-ruler-modal .modal-wrapper>.modal-info>.modal-info.modal-info-content {
    max-height: calc(100vh - 5rem);
    overflow: auto
}

@keyframes loadingCartAdd {
    0% {
        transform: rotate(0)
    }

    100% {
        transform: rotate(360deg)
    }
}

.cart.loading-cart .cart__empty {
    position: relative;
    min-height: 45px
}

.cart.loading-cart .cart__empty:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -30px;
    margin-left: -30px;
    width: 30px;
    height: 30px;
    border-radius: 50px;
    border: 4px solid #efefef;
    border-top-color: #919191;
    animation: 2s linear infinite loadingCartAdd
}

.action-footer-fixed .action-footer-fixed__instagram,.action-footer-fixed__anchor {
    border-radius: 150px;
    z-index: 999;
    background: var(--color_secondary_medium);
    height: 50px;
    width: 50px;
    display: flex;
    box-sizing: border-box;
    transition: .3s;
    color: var(--color_font_inverted);
    cursor: pointer
}

.cart.loading-cart .dropdown__content:has(.cart__empty)~.dropdown__footer {
    display: none
}

.action-footer-fixed__anchor {
    align-items: center;
    justify-content: center;
    font-size: 35px;
    opacity: 0;
    visibility: hidden
}

.action-footer-fixed__anchor .icon {
    font-size: 20px;
    transform: rotate(-90deg)
}

.action-footer-fixed {
    position: fixed;
    right: 15px;
    bottom: 77px;
    display: flex;
    align-items: center;
    flex-direction: column;
    z-index: 99;
    transition: bottom .5s
}

.action-footer-fixed .action-footer-fixed__instagram {
    align-items: center;
    justify-content: center;
    font-size: 35px;
    margin-top: 8px;
    background: linear-gradient(115deg,#f9ce34,#ee2a7b,#6228d7)
}

.action-footer-fixed .action-footer-fixed__instagram .icon {
    font-size: 22px
}

.action-footer-fixed .action-footer-fixed__instagram a {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 50px;
    width: 50px
}

.site-lgpd.loaded,style~div>a[href*=whatsapp]:first-child>svg.whatsapp:first-child {
    display: none
}

@media(min-width: 767px) {
    .smart-filter .filter-list {
        max-height:345px;
        overflow-y: auto;
        scrollbar-color: var(--color_primary_medium) var(--color_gray_dark);
        scrollbar-width: thin
    }

    .smart-filter .filter-list::-webkit-scrollbar {
        width: 6px;
        background: var(--color_gray_dark)
    }

    .smart-filter .filter-list::-webkit-scrollbar-track {
        background: var(--color_gray_dark)
    }

    .smart-filter .filter-list::-webkit-scrollbar-thumb {
        background: var(--color_primary_medium);
        border-radius: 5px
    }

    .footer .footer-main .newsletter .form .field[name=name] {
        max-width: 200px;
        margin-right: 10px
    }

    .nav .product .product-tags-circle .tag-circle.discount {
        font-size: 9px;
        width: 40px;
        height: 40px
    }

    .nav .list-product-countdown .list-product-countdown__timer {
        justify-content: center;
        gap: 0 10px;
        padding: 5px
    }

    .nav .list .second-level.products-show ul.categories-children>li,.nav>.container>.list>.sub.all-categories>.sub-list.second-level>.wrapper-categories>li {
        -webkit-column-break-inside: avoid;
        -moz-column-break-inside: avoid;
        break-inside: avoid-column
    }

    .product-wrapper .product-tabs.long-tabs .tabs-content {
        padding-top: 25px
    }

    header .contact .header-wrapper {
        width: 320px
    }

    .brands-custom .dots,.buy-sizes .dots,.custom-home-reviews .dots,.footer .title .icon.icon-arrow-down,.nav>.container>.list>.sub.all-categories>.sub-list.second-level>li.sub>a,.section-showcase .dots,.template-instagram .dots {
        display: none
    }

    .footer .cols .container>.box.box-pages {
        max-width: 200px
    }

    .brands-custom .container {
        padding: 20px 55px 10px;
        margin-top: -10px;
        margin-bottom: 2.5rem
    }

    .nav .list-product__items .list-product-countdown .item {
        flex-direction: column;
        line-height: 13px
    }

    .nav .list>.first-level span[title=Departamentos] .name {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700
    }

    .footer .cols .container>.box {
        text-align: left;
        width: auto;
        padding: 0 10px;
        max-width: 260px
    }

    .footer .cols .container>.box.payments-seals {
        width: 20%
    }

    .nav .category-image {
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1px
    }

    li.first-level.categoria-offer>a .name {
        display: flex;
        align-items: center;
        padding: 0!important;
        white-space: nowrap;
        font-weight: 600;
        max-width: initial!important
    }

    .nav .list>li>a .name {
        max-width: initial
    }

    li.first-level.categoria-offer>a>.name .category-image img {
        max-width: 22px
    }

    header.header li.all-categories ul ul.sub-list.second-level {
        position: relative;
        border: 0;
        top: 0;
        transform: none;
        min-width: 100%;
        padding: 0;
        max-height: initial!important;
        box-shadow: none;
        overflow: initial;
        margin-bottom: 15px
    }

    .nav .list .second-level.products-show ul.categories-children>li.sub>a,.nav>.container>.list>.sub.all-categories>.sub-list.second-level>.wrapper-categories>li>a,header.header li.all-categories>ul.sub-list.second-level>li>a {
        font-weight: 700
    }

    header .contact.gift .contact-text span {
        font-size: 11px
    }

    .nav>.container>.list>.sub.all-categories {
        position: static
    }

    .nav>.container>.list>.sub.all-categories>.sub-list.second-level {
        align-items: flex-start;
        justify-content: flex-start;
        display: flex;
        align-content: start;
        max-height: 60vh!important;
        flex-direction: column;
        max-width: initial!important;
        min-width: initial;
        width: 100%;
        overflow: auto;
        left: 0
    }

    .nav>.container>.list>.sub.all-categories>.sub-list.second-level>.wrapper-categories>li {
        padding: 5px 0;
        box-sizing: border-box;
        background: #fff
    }

    .nav>.container>.list>.sub.all-categories>.sub-list.second-level>li>ul.sub-list.second-level>a {
        font-weight: 700;
        margin-bottom: 0;
        margin-top: 0
    }

    li.first-level.categoria-offer {
        position: static!important
    }

    li.first-level.categoria-offer ul.sub-list.second-level {
        display: flex;
        align-items: center;
        padding: 0;
        width: 100%;
        pointer-events: none;
        overflow: auto;
        left: 0
    }

    li.first-level.categoria-offer:hover ul.sub-list.second-level {
        pointer-events: auto
    }

    .first-level.categoria-offer .categoria-offer__products {
        width: 70%;
        box-shadow: 0 0 17px 5px rgb(0 0 0 / 9%);
        padding: 20px 10px;
        max-height: 66vh;
        overflow: auto
    }

    .first-level.categoria-offer .categoria-offer__products::-webkit-scrollbar {
        width: 6px
    }

    .first-level.categoria-offer .categoria-offer__products::-webkit-scrollbar-track {
        background: #f5f5f5
    }

    .first-level.categoria-offer .categoria-offer__products::-webkit-scrollbar-thumb {
        background: #d6d6d6
    }

    .first-level.categoria-offer .categoria-offer__descriptions {
        width: 30%;
        box-sizing: border-box;
        text-align: center;
        padding: 25px 23px;
        border: 0
    }

    .first-level.categoria-offer .categoria-offer__title strong {
        font-size: 35px;
        line-height: normal;
        display: block;
        font-weight: 600
    }

    .first-level.categoria-offer .categoria-offer__title span {
        font-size: 21px;
        line-height: normal;
        display: block;
        font-weight: 400
    }

    .first-level.categoria-offer .categoria-offer__countdown {
        display: flex;
        justify-content: center;
        margin-top: 25px;
        padding: 5px 0;
        align-items: center
    }

    .first-level.categoria-offer .categoria-offer__countdown li.item {
        width: 60px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
        margin: 0;
        background: 0;
        border-radius: 3px;
        font-size: 37px
    }

    .first-level.categoria-offer .categoria-offer__countdown li.item span {
        font-weight: 600;
        font-size: 13px;
        color: #787878;
        display: block;
        line-height: normal
    }

    .first-level.categoria-offer .categoria-offer__countdown li.item i {
        font-style: normal;
        margin-bottom: 3px;
        font-weight: 600;
        color: var(--color_primary_medium);
        margin-top: 3px;
        font-size: 34px
    }

    .bg.header-desktop .search.flex.align-center i,.bg.header-desktop.active .search.flex.align-center svg,.first-level.categoria-offer .categoria-offer__products .section-header,.first-level.categoria-offer .categoria-offer__products:not(.active-slide) .next,.first-level.categoria-offer .categoria-offer__products:not(.active-slide) .prev,.header .bg.header-mobile {
        display: none
    }

    .first-level.categoria-offer .categoria-offer__products .section-showcase .swiper-container {
        margin: 0;
        padding: 0
    }

    li.first-level.categoria-offer>a {
        background: var(--color_secondary_medium);
        color: var(--color_font_inverted);
        height: 40px;
        border-radius: var(--border_radius_buttons);
        margin-top: 5px;
        padding: 0 17px;
        margin-bottom: 5px
    }

    .first-level.categoria-offer .categoria-offer__products .list-product__items .item {
        width: calc(100% / 3);
        margin-bottom: 0;
        padding: 0 6px
    }

    .header-info .header-info__content li:last-child {
        border-right: 0
    }

    .search.flex.align-center svg {
        width: 26px;
        margin-top: -2px
    }

    .search.flex.align-center {
        padding: 8px;
        cursor: pointer
    }

    .search.flex.align-center .serch-icon {
        display: flex;
        align-items: center
    }

    .search.flex.align-center .serch-icon i {
        font-size: 21px
    }

    .bg.header-desktop .search.flex.align-center svg,.bg.header-desktop.active .search.flex.align-center i,.header .bg.header-desktop {
        display: block
    }

    html:not(.page-home) .header {
        border-bottom: 1px solid #ebebeb
    }

    .header .bg.header-desktop .header-right {
        display: flex;
        flex: 0 0 auto;
        width: auto;
        margin-left: 25px
    }

    .header .bg.header-desktop .search-move {
        display: flex;
        justify-content: flex-end;
        flex-grow: 1;
        padding-left: 15px
    }

    .page-home main.site-main>div {
        margin-bottom: 2.5rem
    }

    .page-home main.site-main .section-showcase {
        margin-bottom: 2rem
    }

    ul.list.flex.justify-center>.first-level {
        margin: 0 20px
    }

    .nav .list .second-level.products-show {
        display: flex;
        width: 800px
    }

    .nav .list .second-level.products-show .category-product {
        width: 220px
    }

    .nav .list .second-level.products-show ul.categories-children {
        flex-grow: 1;
        -webkit-columns: 2;
        -moz-columns: 2;
        columns: 2;
        height: 100%;
    }

    .first-level.categoria-offer .categoria-offer__descriptions .categoria-offer__button {
        text-align: center;
        width: 100%;
        background: var(--color_secondary_medium);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        color: var(--color_font_inverted);
        border-radius: var(--border_radius_buttons);
        margin: 20px auto 0;
        font-weight: 700;
        max-width: 230px
    }

    .first-level.categoria-offer .categoria-offer__countdown li.blank {
        font-size: 30px
    }

    .nav .list .product .product-tags .tag {
        font-size: 8px;
        margin: 3px 0 1px;
        min-width: 65px;
        max-width: 85px
    }

    .product-wrapper .product-gallery .product-thumbs .swiper-slide .thumb {
        height: 100%;
        max-height: 100%
    }

    .product-wrapper .product-gallery .product-thumbs .thumbs-list {
        height: 450px;
        width: 100%
    }

    .compreJunto form .fotosCompreJunto:has(.plus.color.to:nth-child(4)) {
        width: 700px
    }

    .compreJunto form {
        justify-content: center
    }

    .compreJunto form .fotosCompreJunto:has(.plus.color.to:nth-child(4)) .produto {
        width: 45%
    }

    .nav .product .product-price .product-message span.seal-not-stock {
        font-size: 10px;
        top: 7px
    }

    .nav .list.justify-between>li:not(.categoria-offer)>a,.nav .list.justify-between>li:not(.categoria-offer)>span {
        padding-left: 0;
        padding-right: 0
    }

    .list-product .item .compare-buttons~.product-info .seal-not-stock {
        top: 38px
    }

    .nav .list .second-level.products-show ul.categories-children>li.sub {
        margin-bottom: 10px
    }

    .banner-home .prev {
        left: 20px
    }

    .banner-home .next {
        right: 20px
    }

    .footer .cols .container>.box.box-infos {
        max-width: 350px
    }
}

.cart .cart__user-actions {
    border-top: 1px solid #ddd;
    padding: 19px 0 0;
    margin: 19px 0 0
}

.cart .cart__user-actions p {
    flex: 1;
    line-height: 17px
}

.cart .cart__user-actions div {
    display: flex;
    justify-content: space-between
}

.cart .cart__user-actions div a {
    width: 100%;
    text-align: center;
    box-sizing: border-box;
    border-bottom: 1px solid #ddd;
    transition: .3s;
    padding: 5px 0;
    font-weight: 600
}

.cart .cart__user-actions div a.cart__user-actions-register {
    color: var(--color_font_medium)
}

.cart .cart__user-actions div a.cart__user-actions-login {
    color: var(--color_primary);
    border-color: 1px solid var(--color_primary)
}

.cart .cart__user-actions .cart__user-actions-text {
    margin: 0 0 12px;
    gap: 8px
}

.cart .cart__user-actions .cart__user-actions-text svg {
    fill: none;
    position: relative;
    top: 2px
}

.cart .cart__user-actions .cart__user-actions-links {
    gap: 5px
}

@media(max-width: 767px) {
    .menu-mobile .menu-mobile-actions,.nav-mobile .list li.categoria-offer a {
        color:var(--color_font_inverted);
        background: var(--color_secondary_medium)
    }

    .section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header,.section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header .title-section {
        text-align: center
    }

    .compreJunto .fotosCompreJunto .plus.color.to,.footer .cols .container>.box:not(.payments-seals):not(.box-logo) .overflow,.header .account>div,.header .bg.header-desktop,.header-info,.message-top .dots,.product-tabs .tabs-content .payment-tab .option-details table td:last-child,.product-tabs .tabs-content .payment-tab .option-details table th:last-child,.product-tabs .tabs-nav,.product-wrapper .product-tabs .tabs-nav,.product__buy .product__buy__fields .product__form .label-quantity,.section-notices .noticias>li:nth-child(n+3),.section-showcase .dots,.section-showcase.not-slide-drag .swiper-button-disabled,header .contact {
        display: none
    }

    .block-custom .block-custom__content:not(.swiper-wrapper) {
        flex-wrap: wrap;
        align-items: flex-start
    }

    .block-custom .block-custom__content .item {
        width: calc(100% / 2);
        padding: 12px 10px
    }

    .block-custom .block-custom__content .item a,.product-wrapper .product-box .product-gallery,.product-wrapper .product-form .product-shipping,.quick-buy .quick-buy__content .quick-buy__action .page-product__quantity-and-buy,.section-showcase.section-showcase--countdown .wrapper-header-countdown {
        flex-direction: column
    }

    .banners-grid .item {
        padding: 0;
        width: 100%;
        margin: 0 0 20px
    }

    .banners-grid .item:last-child,.block-custom.custom-home-reviews .block-custom__content .item,.product .product-price .product-has-variants {
        margin-bottom: 0
    }

    .banners-grid.banner-showcase {
        margin-top: 35px
    }

    .banners-grid.banner-showcase .container,.slide-catalog.swiper-container .swiper-wrapper {
        flex-wrap: wrap
    }

    .footer .copy {
        padding: 1rem 0 .5rem;
        border-top: 1px solid #eee
    }

    .block-custom {
        padding: 5px 0;
        margin-top: 0
    }

    .header .cart-toggle .icon {
        margin-right: 4px
    }

    .header .cart-toggle .icon svg {
        width: 25px;
        height: 25px
    }

    .header-search-wrapper .button-search {
        width: 45px;
        top: calc(50% - 20px);
        height: 40px
    }

    header.header {
        position: sticky;
        top: -1px;
        left: 0;
        z-index: 9;
        border-bottom: 1px solid var(--color_header_details)
    }

    .header .account .account-icon.icon svg {
        width: 38px;
        margin-bottom: -2px
    }

    .section-header {
        margin-bottom: 25px
    }

    .cart .dropdown__content {
        padding-right: 0
    }

    .cart section#cart {
        padding: 15px;
        max-width: 85%
    }

    .product .product-price .product-message span.seal-not-stock {
        font-size: 10px;
        padding: 3px 12px 4px;
        right: 5px;
        top: 5px
    }

    .nav-mobile .category-image {
        max-width: 23px;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: -5px
    }

    .banners-grid .item a img,.footer .cols .container>.box.payments-seals>.overflow,.nav-mobile .category-image img,.product-wrapper .product-gallery .product-images,.template-instagram div#instafeed a:nth-child(3):last-child,.template-instagram div#instafeed a:nth-child(5):last-child,.template-instagram div#instafeed a:nth-child(7):last-child {
        width: 100%
    }

    .buy-sizes {
        margin-bottom: 0;
        margin-top: 30px
    }

    .message-top .swiper-wrapper {
        align-items: center
    }

    .message-top .item a {
        margin: 0 auto;
        padding: 9px 10px;
        line-height: 17px
    }

    .nav-mobile ul.list.first-level>li.sub>a {
        display: flex;
        justify-content: flex-start;
        font-weight: 700
    }

    .nav-mobile ul.list.first-level>li.sub>a i.icon.icon-arrow-down {
        position: absolute;
        right: 0;
        top: 8px
    }

    .swiper-container .dots {
        margin-top: 5px
    }

    .banner-footer-pages {
        margin-top: 15px
    }

    .banners-grid .container.flex.f-wrap.justify-between {
        flex-wrap: wrap;
        gap: 0
    }

    .brands-custom .brands-custom__content .item {
        width: auto;
        max-width: 100%
    }

    .banners-grid.banners-showcase .item:first-child,.footer .cols .container>.box.box-logo {
        margin-bottom: 20px
    }

    .slide-catalog.swiper-container .swiper-wrapper .item.swiper-slide {
        width: calc(100% / 3);
        margin-bottom: 30px
    }

    .header .bg.header-mobile {
        display: block
    }

    .footer .cols .container>.box.payments-seals>.overflow:nth-child(2) {
        margin: 5px 0 0
    }

    .section-showcase .swiper-carousel .next,.section-showcase .swiper-carousel .prev,.section-showcase .swiper-container .next,.section-showcase .swiper-container .prev {
        display: flex;
        top: calc(40% - 20px)
    }

    .actions .product-button,.product__buy .product__buy__fields .product__form button.bt {
        padding: 10px 0;
        font-size: 14px;
        height: 37px
    }

    .product .product-price {
        padding-top: 10px
    }

    .product__buy {
        width: 100%;
        margin-top: 10px;
        padding: 0
    }

    .menu-actions-mobile__contact .header-wrapper__item.header-wrapper__item--truck {
        border-top: 1px solid var(--color_header_details);
        margin-top: 15px;
        padding-top: 15px;
        padding-right: 0
    }

    .menu-actions-mobile__contact .header-wrapper__item.header-wrapper__item--truck .header-wrapper__form {
        display: flex;
        align-items: center;
        position: relative
    }

    .menu-mobile-actions .account.flex.align-center {
        border-bottom: 1px solid var(--color_header_details);
        margin-bottom: 10px;
        padding-bottom: 10px;
        padding-top: 15px;
        height: auto;
        width: 100%
    }

    .menu-actions-mobile__contact li {
        font-size: 13px;
        margin: 12px 0
    }

    .menu-actions-mobile__contact li a span {
        font-weight: 400
    }

    .menu-actions-mobile__contact li.wpp-mobile a {
        display: flex;
        font-weight: 600
    }

    .menu-actions-mobile__contact li.wpp-mobile a .icon:after {
        content: "WhatsApp:";
        font-family: Lato,sans-serif;
        font-size: 12px;
        margin-left: 6px;
        position: relative;
        top: -3px
    }

    .menu-actions-mobile__contact li a i {
        margin-right: 3px;
        font-size: 15px;
        margin-top: -1px
    }

    .menu-actions-mobile__contact .header-wrapper__item.header-wrapper__item--truck .header-wrapper__form input[type=text] {
        height: 38px;
        font-size: 16px;
        padding: 0 0 0 20px;
        width: 100%;
        background: #fff;
        color: #333
    }

    .menu-actions-mobile__contact .header-wrapper__item.header-wrapper__item--truck .header-wrapper__form .header-wrapper__button {
        position: absolute;
        right: 0;
        height: 100%;
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        padding-right: 3px;
        top: -1px;
        color: var(--color_secondary_medium)
    }

    .menu-actions-mobile__contact .header-wrapper__item.header-wrapper__item--truck .header-wrapper__text {
        font-size: 13px;
        padding-bottom: 10px
    }

    .menu-actions-mobile__contact .header-wrapper__item.header-wrapper__item--truck .header-wrapper__text .icon {
        font-size: 16px;
        vertical-align: middle;
        margin-top: -1px;
        margin-right: 3px
    }

    .template-instagram div#instafeed a img {
        height: 45vw
    }

    .product,.section-showcase.section-showcase--countdown .list-product .item .product {
        padding-bottom: 10px
    }

    .section-product-related .list-product {
        padding: 0;
        margin: 0 0 -10px
    }

    .template-instagram div#instafeed a:nth-child(3):last-child img,.template-instagram div#instafeed a:nth-child(5):last-child img,.template-instagram div#instafeed a:nth-child(7):last-child img {
        height: 80vw
    }

    .cart .cart__bt {
        width: 100%;
        font-size: 14px;
        max-width: 550px
    }

    .cart__footer {
        text-align: left;
        justify-content: flex-start
    }

    .cart__footer span.cart__total-price {
        margin-bottom: 14px
    }

    .page-home main.site-main>div {
        margin-bottom: 1.5rem
    }

    .menu-mobile-actions .account.flex.align-center .account-icon {
        font-size: 17px;
        margin-right: 7px;
        color: currentColor
    }

    .brands-custom {
        padding-bottom: 15px
    }

    .footer .cols .container {
        flex-direction: column;
        padding: 0
    }

    .section-notices .noticias>li {
        width: calc(100% / 2)
    }

    .section-notices .noticias {
        gap: 10px
    }

    .footer .copy .tray {
        align-items: stretch
    }

    #NavLogoTray .logotray-message,.footer .copy .tray .credits-partner span {
        flex-direction: column;
        align-items: flex-start
    }

    #NavLogoTray a:after,.footer .copy .tray .credits-partner span img {
        margin: 0
    }

    .footer .box.payments-seals {
        margin-bottom: 0;
        padding-top: 0
    }

    .footer .cols .container>.box:not(.payments-seals):not(.box-logo) {
        padding: 0 15px;
        margin-bottom: 20px
    }

    .footer .cols .container>.box:not(.payments-seals):not(.box-logo) .title {
        justify-content: space-between;
        display: flex;
        align-items: center
    }

    .footer .cols .container>.box:not(.payments-seals):not(.box-logo) .title.active .icon {
        transform: rotate(180deg)
    }

    .footer .cols .container>.box:not(.payments-seals):not(.box-logo) .title .icon {
        font-size: 16px;
        transition: .3s
    }

    .footer .footer-main .newsletter .form .news-button {
        height: 45px;
        width: 100%;
        margin: 0
    }

    .footer .title {
        font-size: 18px
    }

    .nav-mobile .list li.categoria-offer a {
        height: 40px;
        border-radius: var(--border_radius_buttons);
        margin-top: 5px;
        padding: 1px 0 3px;
        margin-bottom: 5px;
        justify-content: center;
        font-weight: 700
    }

    .account div>span {
        font-size: 13px
    }

    .account .login-links span,.account a,.account div {
        font-size: 13px;
        color: currentColor
    }

    .header .line .header-mobile__side-left {
        width: 50px
    }

    .header .line .header-mobile__side-right {
        width: 130px;
        display: flex;
        align-items: center;
        justify-content: flex-end
    }

    .header .line .header-mobile__side-center {
        width: calc(100% - 181px)
    }

    .menu-mobile .menu-mobile-actions {
        padding: 0 15px;
        margin-left: -15px;
        margin-right: -15px
    }

    .menu-actions-mobile__contact .header-wrapper__item.header-wrapper__item--truck .header-wrapper__form input[type=text]::placeholder {
        color: currentColor
    }

    .block-custom.banner-stripe .block-custom__content.swiper-wrapper {
        height: auto
    }

    .block-custom.banner-stripe .dots,.product-wrapper .product-tabs {
        margin-top: 0
    }

    .buy-whatsapp.buy-whatsapp--list-product,.product-wrapper .product-gallery .product-thumbs .swiper-slide,.quick-buy .container {
        padding: 0
    }

    .buy-whatsapp a,.fotosCompreJunto .plus.to:before,.fotosCompreJunto .plus:before {
        font-size: 9px
    }

    .product .product-info .wrapper-product-name {
        height: 40px
    }

    .product-tabs .tabs-content .tab-link-mobile {
        display: block;
        position: relative;
        width: 100%;
        padding: 15px 0;
        font-size: 15px;
        font-weight: 500;
        text-decoration: none;
        border-bottom: 1px solid var(--color_gray_dark);
        transition: .2s ease-out;
        text-align: left
    }

    .product-tabs .tabs-content .tab-link-mobile:first-child {
        border-top: 1px solid var(--color_gray_dark)
    }

    .product-tabs .tabs-content .tab-link-mobile:after {
        content: "\ea01";
        position: absolute;
        top: 19px;
        right: 0;
        margin-left: .3125rem;
        font-family: go!important;
        font-size: .75rem;
        font-style: normal!important;
        font-weight: 400!important;
        font-variant: normal!important;
        text-transform: none!important;
        transition: .2s ease-out
    }

    .product-tabs .tabs-content .tab-link-mobile.active {
        color: var(--color_primary_medium)
    }

    .product-tabs .tabs-content .tab-link-mobile.active:after {
        transform: rotate(-180deg)!important
    }

    .product-tabs .tabs-content .tab {
        display: none;
        padding: 8px 0
    }

    .product-tabs .tabs-content .tab.active {
        border-bottom: 1px solid var(--color_gray_dark);
        transition: border .1s ease-out .3s
    }

    .product-tabs .tabs-content .tab.payment-tab {
        padding-top: .625rem;
        padding-bottom: 1.25rem
    }

    .product-tabs .tabs-content .payment-tab .option-details table td:first-child,.product-tabs .tabs-content .payment-tab .option-details table th:first-child {
        width: 20%
    }

    .product-tabs .tabs-content .payment-tab .option-details table td:nth-child(2),.product-tabs .tabs-content .payment-tab .option-details table th:nth-child(2) {
        width: 80%
    }

    .product-tabs .tabs-content .payment-tab .option:last-child a {
        border-bottom: 0
    }

    .product-tabs .tabs-content .payment-tab .option:last-child.show a {
        border-bottom: 1px solid var(--color_gray_dark)
    }

    .product-tabs .tabs-content .payment-tab .option:last-child.show .option-details {
        border-bottom: 0;
        padding-bottom: 0
    }

    .product-wrapper .product-form .product-shipping .info {
        width: 100%;
        padding: 0 0 10px;
        justify-content: flex-start
    }

    .product-wrapper .product-form .product-shipping .shipping-form {
        max-width: 100%
    }

    .section-showcase.section-showcase--countdown .wrapper-header-countdown .section-header a {
        margin: 10px auto 20px
    }

    .section-showcase.section-showcase--countdown .wrapper-header-countdown .wrapper-header-countdown__timer i {
        font-size: 26px
    }

    .section-showcase.section-showcase--countdown .wrapper-header-countdown .wrapper-header-countdown__timer .item span {
        font-size: 14px
    }

    .list-product-countdown.list-product-countdown--showcase .list-product-countdown__timer {
        justify-content: center;
        padding: 4px 0
    }

    .list-product-countdown.list-product-countdown--showcase .list-product-countdown__timer .item {
        flex-direction: column;
        padding: 0 5px!important
    }

    .list-product-countdown.list-product-countdown--showcase .list-product-countdown__timer .item span {
        font-size: 8px;
        margin: -2px 0 0
    }

    .list-product-countdown.list-product-countdown--showcase .list-product-countdown__timer .item i {
        font-size: 13px
    }

    .section-showcase.section-showcase--countdown .buy-whatsapp.buy-whatsapp--list-product,.section-showcase.section-showcase--countdown .product .actions,.section-showcase.section-showcase--countdown .product__buy {
        padding: 0 10px
    }

    .header .bg.header-mobile .search-mobile {
        display: none;
        align-items: center;
        margin-right: 9px;
        position: relative
    }

    .header .bg.header-mobile.search-closed .header-mobile__side-right .search-mobile svg {
        width: 25px;
        color: var(--color_header_highlight);
        transition: .3s;
        margin-bottom: -1px
    }

    .header .bg.header-mobile.search-closed .search-mobile,.section-avaliacoes .dots.swiper-pagination-bullets {
        display: flex
    }

    .header .bg.header-mobile.search-closed .header-mobile__side-right .account {
        margin-right: 7px
    }

    .header .bg.header-mobile.search-closed .header-mobile__side-left {
        width: 40px
    }

    .header .bg.header-mobile.search-closed .header-mobile__side-center {
        width: calc(100% - 171px);
        padding-right: 10px
    }

    .header .bg.header-mobile.search-closed .header-search-wrapper {
        position: absolute;
        bottom: -80px;
        padding: 15px;
        background: #fff;
        left: 0;
        margin: 0;
        box-shadow: 0 2px 5px 0 rgb(0 0 0 / 8%);
        transition: .3s;
        opacity: 0;
        visibility: hidden
    }

    .header .bg.header-mobile.search-closed .header-search-wrapper .button-search {
        right: 12px
    }

    .header .bg.header-mobile.active-search .header-search-wrapper {
        bottom: -60px;
        opacity: 1;
        visibility: visible
    }

    .header .bg.header-mobile .search-mobile:after,.header .bg.header-mobile .search-mobile:before {
        content: "";
        width: 27px;
        position: absolute;
        background: var(--color_header_highlight);
        height: 1px;
        top: 12px;
        left: 0;
        transform: rotate(45deg);
        border-radius: 50px;
        opacity: 0;
        transition: .3s;
        visibility: hidden
    }

    .header .bg.header-mobile .search-mobile:after {
        transform: rotate(-45deg)
    }

    .header .bg.header-mobile.active-search .search-mobile:after,.header .bg.header-mobile.active-search .search-mobile:before {
        opacity: 1;
        visibility: visible
    }

    .header .bg.header-mobile.active-search .search-mobile svg {
        opacity: 0;
        visibility: hidden
    }

    .page-navegacao_visitados div#Vitrine {
        grid-template: repeat(1,1fr)/repeat(2,1fr);
        gap: 5px;
        display: flex;
        flex-wrap: wrap
    }

    .page-navegacao_visitados .vitrineVisitados span.valores div#precoDe {
        font-size: 11px;
        margin: 1px 0 0
    }

    .page-navegacao_visitados .vitrineVisitados span.valores .preco-avista {
        margin: 4px 0 0;
        font-size: 15px
    }

    .page-navegacao_visitados .vitrineVisitados span.valores .preco-avista:after,.page-navegacao_visitados .vitrineVisitados span.valores .preco-avista:before {
        font-size: 10px
    }

    .page-navegacao_visitados .vitrineVisitados>li {
        background: #fff;
        box-shadow: 1px 1px 3px 0 rgb(0 0 0 / 5%);
        width: 49%;
        padding-bottom: 20px
    }

    .page-navegacao_visitados .vitrineVisitados span.valores span.precoAvista:not(.preco-avista) {
        font-size: 15px;
        margin: 4px 0 3px;
        padding: 0;
        text-align: center;
        width: 100%
    }

    .page-navegacao_visitados .ranking.hreview-aggregate,.quick-buy #produto_preco #info_preco {
        font-size: 11px
    }

    .page-navegacao_visitados .vitrineVisitados a.NomeProdLista {
        padding: 0;
        font-size: 11px;
        line-height: 12px;
        margin: 12px 0 0;
        height: 35px;
        -webkit-line-clamp: 3
    }

    .page-navegacao_visitados div#vitrine-catalogo>.container3:not(.central-conteudo) .container2 .left {
        padding: 18px 0 0
    }

    .page-navegacao_visitados .board {
        order: 0
    }

    .compre-junto,.page-navegacao_visitados a.Mapa.central-mapa {
        margin: 0 0 30px
    }

    .page-navegacao_visitados .vitrineVisitados>li>a.Foto {
        min-height: 37vw;
        min-height: initial;
        height: auto;
        overflow: hidden
    }

    .page-navegacao_visitados .vitrineVisitados:first-child>li:first-child:empty {
        background: 0;
        box-shadow: none!important
    }

    .page-navegacao_visitados .vitrineVisitados {
        margin: 0;
        width: 100%;
        max-width: 100%
    }

    .page-navegacao_visitados .vitrineVisitados>li>a.Foto img {
        height: auto;
        width: 127%
    }

    .page-navegacao_visitados .vitrineVisitados>li>a.Foto:after {
        display: none!important
    }

    .product .product-tags .tag {
        font-size: 7px;
        margin-top: 1px;
        margin-bottom: 3px;
        max-width: 70px;
        min-width: 70px;
        max-height: 70px
    }

    .product .product-tags-circle .tag-circle.discount {
        font-size: 8px
    }

    .product .product-tags-circle {
        right: 7px;
        top: 7px
    }

    .compreJunto .produto .product-name {
        margin-bottom: 3px;
        font-size: 12px
    }

    .compreJunto .produto>span {
        margin-bottom: 5px
    }

    .compreJunto .varCont input[type=text],.compreJunto .varCont select {
        font-size: 10px;
        padding: 0 10px;
        height: 30px
    }

    .products-history #produtos ul {
        margin: 5px 0
    }

    .products-history {
        margin-top: 1.7rem
    }

    .product-wrapper .product-gallery .product-thumbs {
        order: 5;
        height: auto;
        width: 100%;
        margin: 10px 0 0
    }

    .template-instagram .swiper-container .dots {
        margin-top: 20px
    }

    .quick-buy .quick-buy__content .quick-buy__image {
        margin-right: 10px
    }

    .quick-buy .quick-buy__content .quick-buy__name strong {
        font-size: 12px;
        line-height: normal;
        display: block
    }

    .quick-buy #produto_preco .PrecoPrincipal {
        font-size: 14px;
        margin-bottom: 0
    }

    .quick-buy .quick-buy__content .quick-buy__action.quick-top {
        width: auto
    }

    .quick-buy .quick-buy__content .quick-buy__price {
        max-width: initial;
        margin: 0 19px 0 0
    }

    .quick-buy .quick-buy__content .quick-buy__name {
        display: none
    }

    .quick-buy .quick-buy__content .quick-buy__action .page-product__quantity input#quant {
        height: 30px;
        width: 100%
    }

    .quick-buy .quick-buy__content .quick-buy__action button#button-buy {
        height: 35px;
        font-size: 13px
    }

    .quick-buy .quick-buy__content .quick-buy__action {
        width: auto;
        max-width: 110px
    }

    .quick-buy .quick-buy__content .quick-buy__action .page-product__quantity {
        margin: 0 0 5px;
        width: 100%
    }

    .quick-buy .quick-buy__content .quick-buy__action.quick-top button.to-top {
        font-size: 12px;
        height: 40px
    }

    body:has(.quick-buy.active) .fixed-whatsapp {
        bottom: 100px
    }

    .cart .cart__user-actions p,.dropdown__footer.align-center>p {
        font-size: 14px;
        line-height: 16px
    }

    .cart .cart__user-actions .cart__user-actions-text svg {
        top: 0;
        width: 24px
    }

    .list-product .item .compare-buttons~.product-info .seal-not-stock {
        top: 33px
    }

    .header .bg.header-mobile.search-closed .header-mobile__side-left .header-menu div {
        background-color: var(--color_header_highlight)
    }

    .product-wrapper .product-gallery .product-thumbs .swiper-slide img {
        border-radius: 5px;
        height: auto
    }

    .banner-home .prev {
        left: 10px
    }

    .banner-home .next {
        right: 10px
    }

    .banner-home .swiper-container .dots {
        bottom: 2px
    }

    .modal:has(.brinde_detalhes .detalhes) .modal-dialog.modal-dialog-center {
        left: 0;
        right: 0;
        margin: auto!important;
        display: flex;
        align-items: center;
        justify-content: center;
        transform: none!important;
        top: calc(30vh - 20vh)
    }
}

.site-lgpd {
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 35px;
    border: 1px solid #eee;
    position: fixed;
    bottom: 20px;
    width: 1200px;
    margin: 0 auto;
    left: 0;
    right: 0;
    max-width: 90%;
    z-index: 99999999999999999;
    box-shadow: 0 0 45px 0 rgb(0 0 0 / 12%);
    border-radius: 11px
}

.site-lgpd .site-lgpd__button {
    flex: 0 0 auto;
    width: auto;
    padding: 10px 25px;
    background: var(--color_secondary_medium);
    color: var(--color_font_inverted);
    border-radius: var(--border_radius_buttons);
    cursor: pointer;
    margin-left: 30px;
    font-size: 15px
}

.site-lgpd p {
    font-size: 17px
}

.col-content.load-infinite-products .catalog-footer {
    height: 0;
    overflow: hidden;
    opacity: 0;
    visibility: hidden
}

.col-content.load-infinite-products .catalog-content .showcase-catalog .list-product.load-list:has(.item):after {
    display: none
}

.col-content.load-infinite-products .catalog-content .showcase-catalog .list-product.load-list:not(:last-child):after,.col-content.load-infinite-products.load-products-success .list-product.loaded-list:last-child:after,.custom-showcase-cart {
    display: none
}

.col-content.load-infinite-products .catalog-content .showcase-catalog .list-product.load-list:after {
    content: '';
    width: 45px;
    height: 45px;
    border-radius: 50px;
    border: 4px solid #ebebeb;
    border-top-color: var(--color_secondary_medium);
    animation: 2s linear infinite loadingCustom;
    margin: 0 auto
}

@keyframes loadingCustom {
    0% {
        transform: rotate(0)
    }

    100% {
        transform: rotate(360deg)
    }
}

@media(max-width: 1080px) and (min-width:769px) {
    header.header.header-center .header-desktop .line .search-move {
        max-width:300px
    }

    header.header.header-center .header-search-wrapper .input-search {
        font-size: 13px;
        padding: 0 48px 0 20px
    }

    header.header.header-center .header-desktop .header-right {
        width: 300px
    }

    header.header.header-center .contact .contact-text {
        display: none
    }
}

.progress-bar-custom .progress-bar-custom__percentage {
    z-index: 1;
    background: var(--color_secondary_medium);
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    transition: .9s;
    background-size: 1rem 1rem;
    text-align: right;
    max-width: 100%;
    border-radius: 20px
}

.progress-bar-custom .progress-bar-custom__content {
    position: relative;
    text-align: center;
    padding: 1px 0;
    font-size: 14px;
    height: 15px;
    border-radius: 10px;
    border: 1px solid var(--color_secondary_medium);
    overflow: hidden
}

.progress-bar-custom .progress-bar-custom__descriptions {
    z-index: 2;
    position: relative;
    color: #000;
    font-size: 14px;
    font-weight: 400;
    padding: 7px 0 0;
    text-align: center
}

.progress-bar-custom .progress-bar-custom__descriptions svg {
    width: 22px;
    height: 18px;
    margin-right: 4px
}

.progress-bar-custom {
    margin-bottom: 5px;
    margin-top: 15px;
    border-bottom: 1px solid #ddd;
    padding: 5px 0 17px
}

.suspended-cart .drop-header {
    border-bottom: 1px solid #ddd;
    padding-bottom: 20px
}

@keyframes custom_swing {
    15% {
        -webkit-transform: translateX(3px);
        transform: translateX(3px)
    }

    30% {
        -webkit-transform: translateX(-3px);
        transform: translateX(-3px)
    }

    50%,80% {
        -webkit-transform: translateX(1px);
        transform: translateX(1px)
    }

    65% {
        -webkit-transform: translateX(-1px);
        transform: translateX(-1px)
    }

    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

.progress-bar-custom .progress-bar-custom__descriptions.sucess-bar {
    -webkit-animation: 1s infinite custom_swing;
    animation: 1s infinite custom_swing
}

.cart section#cart .custom-showcase-cart {
    display: block;
    padding: 15px 5px 17px;
    background: #f3f3f3;
    margin-left: -20px;
    margin-right: -20px;
    box-shadow: 0 1px 0 0 #ddd;
    z-index: 2
}

.cart section#cart .custom-showcase-cart .list-product__items .item {
    width: 100%;
    padding: 0;
    margin-bottom: 0
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product {
    display: flex;
    flex-direction: row;
    background: 0 0;
    align-items: center
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .image {
    width: 65px;
    height: 80px;
    top: -6px
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text {
    width: calc(100% - 65px);
    padding-left: 20px
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info {
    padding: 0;
    align-items: flex-start;
    text-align: left
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .price {
    justify-content: flex-start
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .wrapper-product-name {
    height: auto
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product__buy {
    padding: 0;
    font-size: 13px
}

.cart section#cart .custom-showcase-cart .dots,.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .price .product-installments,.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product__buy .label-quantity,.cart section#cart .custom-showcase-cart .section-header .title-section:after {
    display: none
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .product-price {
    min-height: inherit
}

.cart section#cart .custom-showcase-cart .section-header .title-section {
    font-size: 14px;
    text-align: center;
    margin-bottom: 10px
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .product-price .current-price {
    font-size: 14px
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .product-name,.product-wrapper .product-box .product-additional-fields span#ia_complemento {
    font-size: 13px
}

.cart section#cart .custom-showcase-cart .actions .product-button,.cart section#cart .custom-showcase-cart .list-product__items .item .product .product__button,.cart section#cart .custom-showcase-cart .product__buy .product__buy__fields .product__form button.bt {
    font-size: 13px;
    padding: 0;
    height: 30px
}

.cart section#cart .custom-showcase-cart .list-product__items .item .product .image img {
    height: 80px;
    width: 100%;
    object-fit: cover
}

.cart .cart__vue .footer-empty {
    padding: 15px 15px 5px;
    text-align: center
}

.cart section#cart .custom-showcase-cart .list-product {
    padding: 0 35px
}

.cart section#cart .custom-showcase-cart .list-product .next,.cart section#cart .custom-showcase-cart .list-product .prev {
    width: 32px;
    height: 32px;
    font-size: 14px;
    display: flex
}

.cart section#cart .custom-showcase-cart .product__buy .product__buy__fields.product__buy__fields--opened {
    height: 100%;
    overflow: auto;
    position: relative;
    box-shadow: none;
    border-radius: 9px;
    padding: 10px
}

.cart section#cart .custom-showcase-cart .product__buy .select {
    height: 31px;
    font-size: 12px
}

.cart section#cart .custom-showcase-cart .product__buy .product__buy__fields .cor_variacao ul li img {
    height: 25px;
    width: 25px;
    padding: 2px
}

.cart section#cart .custom-showcase-cart .product__buy .product__buy__fields.product__buy__fields--opened .product__form {
    margin-top: 7px
}

.product-wrapper .product-box .product-additional-fields {
    margin-bottom: 10px
}

.product-wrapper .product-box .product-additional-fields input {
    height: 35px
}

.cart__vue .dropdown__footer.align-center>p {
    text-align: center;
    padding: 15px
}

@media(min-width: 768px) {
    .product .product-tags-circle .tag-circle.discount .discount-value {
        font-size:11px;
        line-height: 12px
    }

    body:has(.quick-buy.active) .action-footer-fixed {
        bottom: 188px
    }
}

@media(max-width: 768px) {
    .site-lgpd {
        flex-direction:column;
        text-align: center;
        padding: 15px
    }

    .site-lgpd .site-lgpd__button {
        margin: 10px 0 0;
        font-size: 13px
    }

    .site-lgpd p {
        font-size: 14px
    }

    .product-wrapper .product-form .product-tabs.short-tabs .tabs-content>div#descricao {
        display: block
    }

    .header.header-center-mobile .bg.header-mobile .line .header-mobile__side-left,.header.header-center-mobile .bg.header-mobile .line .header-mobile__side-right {
        width: 80px
    }

    .header.header-center-mobile .bg.header-mobile .header-mobile__side-center {
        padding: 0;
        width: calc(100% - 160px)
    }

    .header.header-center-mobile .bg.header-mobile .header-mobile__side-center .logo {
        justify-content: center
    }

    .header.header-center-mobile .bg.header-mobile .line .header-mobile__side-right .search-mobile.flex.align-center {
        position: absolute;
        left: 51px;
        height: 25px;
        width: 25px;
        top: calc(50% - 13.5px);
        z-index: 1
    }

    .header.header-center-mobile .cart-toggle {
        margin-right: 5px
    }

    body:has(.quick-buy.active) .action-footer-fixed {
        bottom: 158px
    }

    .product .product-tags-circle .tag-circle.discount .discount-value {
        font-size: 10px
    }

    .product-wrapper .product-tabs.long-tabs {
        padding: 35px 0
    }

    .cart section#cart .custom-showcase-cart {
        padding: 10px 10px 0;
        margin-left: -15px;
        margin-right: -15px
    }

    .cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .product-name,.cart section#cart .custom-showcase-cart .section-header .title-section {
        font-size: 11px
    }

    .cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product__buy {
        margin: 0
    }

    .cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .product-price {
        padding: 0
    }

    .progress-bar-custom {
        margin-top: 7px
    }

    .progress-bar-custom .progress-bar-custom__descriptions {
        font-size: 13px
    }

    .progress-bar-custom .progress-bar-custom__content {
        height: 11px
    }

    .cart__product {
        padding: 12px 0
    }

    .cart section#cart .custom-showcase-cart .list-product span.product-has-variants {
        font-size: 12px
    }
}

/*Minify 19 07 24*/
.product-wrapper .product-form .product-tags {
    align-items: center
}

.cart section#cart .custom-showcase-cart .list-product-countdown {
    display: none
}

.product .product-tags .tag:has(img) {
    text-align: left;
    justify-content: flex-start;
    border-radius: 0
}

.product-wrapper .product-tags .tag img {
    max-height: 75px;
    max-width: 170px
}

.product-wrapper .product-form .product-tags .tag:has(img) {
    width: auto;
    border-radius: 0;
    height: auto
}

@media(max-width: 768px) {
    .action-footer-fixed,.fixed-whatsapp {
        z-index:10
    }

    .catalog-header .sort-mobile .sort-panel {
        transform: translateX(100%)
    }

    .cart section#cart .custom-showcase-cart .section-header .title-section {
        margin-bottom: 0
    }

    .cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .product-price .current-price,.cart section#cart .custom-showcase-cart .list-product__items .item .product .product-text .product-info .product-price span.old-price {
        font-size: 10px
    }

    .cart section#cart .custom-showcase-cart .list-product span.product-has-variants {
        font-size: 11px!important
    }

    .nav-mobile ul.list.first-level>li.sub.show>a i.icon.icon-arrow-down {
        transform: rotateX(180deg)
    }

    .nav-mobile ul.list.first-level>li.sub>a i.icon.icon-arrow-down {
        transition: .3s
    }
}

.product .product-tags-circle .tag-circle.progressive-discount:before {
    content: "Desconto Progressivo";
    font-size: 10px;
    line-height: 12px;
    margin: 0;
    display: block;
    width: 65px;
    opacity: 0;
    visibility: hidden;
    position: absolute;
    left: 15px;
    float: left
}

.product .product-tags-circle .tag-circle.progressive-discount:hover {
    width: 110px;
    display: flex;
    justify-content: flex-end;
    padding-right: 12px
}

.product .product-tags-circle .tag-circle.progressive-discount:hover:before {
    opacity: 1;
    visibility: visible
}

.product .product-tags-circle {
    align-items: flex-end;
    z-index: 1
}

.cart section#cart .product .product-tags-circle {
    display: none!important
}

@media(max-width: 768px) {
    .product .product-tags-circle .tag-circle.progressive-discount:hover {
        width: 87px;
        padding-right: 9px;
    }

    .product .product-tags-circle .tag-circle.progressive-discount:before {
        font-size: 8px;
        line-height: 9px;
        left: 10px
    }
}

.cart section#cart .custom-showcase-cart .product .product-price .product-message span.seal-not-stock {
    display: none;
}

.cart section#cart .custom-showcase-cart .product .product-price .product-message {
    font-size: 14px;
    line-height: 17px;
    margin: 10px 0 0 0;
    font-weight: 700;
}

.product.upon_request .product-price .seal-not-stock {
    display: none;
}

.header-wrapper .header-wrapper__content .header-wrapper__item:last-child {
    padding-bottom: 0;
    border: 0;
}

.menu-actions-mobile__contact li.email-menu-mobile:last-child {
    padding-bottom: 10px;
}
/*Minify 03 10 24*/