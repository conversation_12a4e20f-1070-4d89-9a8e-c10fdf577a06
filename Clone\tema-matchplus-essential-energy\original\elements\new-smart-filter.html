<form name="form-filter" method="get" class="new-smart-filter">
    <input type="hidden" name="loja" value="{{ store.id }}">
    {% set type = "" %}
    {% if search.word %}
        <input name="palavra_busca" type="hidden" value="{{ search.word }}">
    {% endif %}

    {% if category.id %}
        <input type="hidden" name="categoria" value="{{ category.id }}">
    {% endif %}

    <h2 class="open-filters ">
        <i class="visible-xs visible-sm pull-right">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 459 459"><path d="M178.5 382.5h102v-51h-102v51zM0 76.5v51h459v-51H0zM76.5 255h306v-51h-306v51z" fill="#a0a2a3"/></svg>
        </i>
        {{ Translation('filtrar') }}
    </h2>

    <div class="filter">
        {% if filter_options.categories %}
        <section class="filter__block filter__block--categories">
            <h4 class="filter__title">Categorias</h4>
            <ul class="filter__list">
                {% for category in filter_options.categories %}
                {% set category_applied = category.key in filtered_options.categories ? true : false %}

                <li class="filter__item">
                    <input id="{{ category.key }}" class="filter__input filter__input--category" type="checkbox"
                        name="categories[]" value="{{ category.key_encoded }}"
                        {{ category_applied ? 'checked' }}>
                    <label class="filter__label" for="{{ category.key }}">
                        <span class="filter__name filter__name--category"> {{ category.key }}</span>
                        <span class="filter__text filter__text--count">({{ category.doc_count }})</span>
                    </label>
                </li>
                {% endfor %}
            </ul>
        </section>
        {% endif %}

        {% if filter_options.prices %}
        <section class="filter__block filter__block--prices">
            <h4 class="filter__title">Pre&#231;o</h4>
            <ul class="filter__list">
                {% for price in filter_options.prices.buckets %}
                {% set price_applied = false %}

                <li class="filter__item">
                    {% for price_apply in filtered_options.price %}
                    {% set price_applied = price.from and price.to in price_apply ? true : false %}
                    {% endfor %}

                    <input id="{{ price.from }}-{{ price.to }}" class="filter__input filter__input--price"
                        type="checkbox" name="prices[]" value="{{ price.from }},{{ price.to }}"
                        {{ price_applied ? 'checked' }}>
                    <label class="filter__label" for="{{ price.from }}-{{ price.to }}">
                        <span class="filter__name filter__name--price">
                            {% if loop.index == 1 %}
                                At&#233; {{ settings.currency }} {{ price.to|currency }}
                            {% elseif loop.index == filter_options.prices.buckets|length  %}
                                Acima de {{ settings.currency }} {{ price.from|currency }}
                            {% else %}
                                De {{ settings.currency }} {{ price.from|currency }} &#224; {{ settings.currency }}{{ price.to|currency }}
                            {% endif %}
                        </span>
                    </label>
                </li>
                {% endfor %}
            </ul>
        </section>
        {% endif %}

        {% if filter_options.availability %}
        <section class="filter__block filter__block--availability">
            <h4 class="filter__title">Disponibilidade</h4>

            <ul class="filter__list">
                {% for availability in filter_options.availability %}
                {% set availability_applied = availability.key in filtered_options.availability ? true : false %}

                <li class="filter__item">
                    <input id="{{ availability.key }}" class="filter__input filter__input--availability" type="checkbox"
                        name="availability[]" value="{{ availability.key_encoded }}"
                        {{ availability_applied ? 'checked' }}>
                    <label class="filter__label" for="{{ availability.key }}">
                        <span class="filter__name filter__name--availability">{{ availability.key }}</span>
                        <span class="filter__text filter__text--count">({{ availability.doc_count }})</span>
                    </label>
                </li>
                {% endfor %}
            </ul>
        </section>
        {% endif %}

        {% if filter_options.brands %}
        <section class="filter__block filter__block--brands">
            <h4 class="filter__title">Marcas</h4>

            <ul class="filter__list">
                {% for brand in filter_options.brands %}
                {% set brands_applied = brand.key in filtered_options.brand ? true : false %}

                <li class="filter__item">
                    <input id="{{ brand.key }}" class="filter__input filter__input--brand" type="checkbox"
                        name="brands[]" value="{{ brand.key_encoded }}"
                        {{ brands_applied ? 'checked'}}>
                    <label class="filter__label" for="{{ item.value }}">
                        <span class="filter__name filter__name--brand">{{ brand.key }}</span>
                        <span class="filter__text filter__text--count">({{ brand.doc_count }})</span>
                    </label>
                </li>
                {% endfor %}
            </ul>
        </section>
        {% endif %}

        {% if filter_options.properties %}
            {% for properties in filter_options.properties %}
                {% for prop in properties %}
                <section class="filter__block filter__block--properties">
                    <ul class="filter__list">
                        <h4 class="filter__title">{{ prop.key }}</h4>
                        {% for item in prop.values %}

                        {% set filter_prop = prop.key ~'||'~ item.key %}
                        {% set filter_encode = prop.key_encoded ~'||'~ item.key_encoded %}


                        {% if item.entity == 'variants' %}
                            {% set prop_applied = filter_prop in filtered_options.variant ? true : false %}
                        {% else %}
                            {% set prop_applied = filter_prop in filtered_options.properties ? true : false %}
                        {% endif %}

                        <li class="filter__item">
                            <input id="{{ item.key }}" class="filter__input filter__input--propertie" type="checkbox"
                                name="{{item.entity}}[]" value="{{ filter_encode }}"
                                {{ prop_applied ? 'checked'}}>
                            <label class="filter__label" for="{{ item.key }}">
                                <span class="filter__name filter__name--propertie"> <img src="{{ item.image_color_secure }}"> {{ item.key }}</span>
                                <span class="filter__text filter__text--count">({{ item.doc_count }})</span>
                            </label>
                        </li>

                        {% endfor %}
                    </ul>
                </section>

                {% endfor %}

            {% endfor %}
        {% endif %}

    <button type="submit" class="filter__button">{{ Translation('filtrar') }}</button>

    </div>
</form>