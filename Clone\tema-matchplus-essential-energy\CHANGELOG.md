# Changelog

Todas as mudanças notáveis neste projeto serão documentadas neste arquivo.

O formato é baseado em [Keep a Changelog](https://keepachangelog.com/pt-BR/1.0.0/),
e este projeto adere ao [Semantic Versioning](https://semver.org/lang/pt-BR/).

## [1.1.0] - 2024-01-08

### ✅ Adicionado
- **Links Personalizados no Menu Superior**
  - Interface de configuração no painel administrativo
  - Suporte a até 3 links personalizados configuráveis
  - Opção para abrir links em nova aba
  - Funcionalidade disponível no menu desktop e mobile
  - Estilos CSS integrados com o tema

### 📝 Arquivos Modificados
- `configs/settings.html` - Adicionada seção "Links Personalizados do Menu"
- `elements/snippets/menu.html` - Implementada lógica para links personalizados no menu desktop
- `elements/snippets/menu-mobile.html` - Implementada lógica para links personalizados no menu mobile
- `css/custom.css.html` - Adicionados estilos CSS para os novos links
- `configs/settings.json` - Adicionado exemplo de configuração

### 🎯 Funcionalidades
- **Configuração via Painel Admin**: Acesso fácil através de Personalizar Tema
- **URLs Flexíveis**: Suporte a URLs internas (`/sintomas`) e externas (`https://exemplo.com`)
- **Responsivo**: Funciona perfeitamente em desktop e mobile
- **Integração Visual**: Estilos que seguem o padrão do tema MatchPlus
- **Controle de Abertura**: Opção para abrir links em nova aba

### 🔧 Detalhes Técnicos
- **Posicionamento**: Links aparecem após as categorias e antes do menu de ofertas
- **Lógica Condicional**: Links só aparecem se nome e URL estiverem preenchidos
- **Codificação**: Suporte adequado a caracteres especiais em português
- **Compatibilidade**: Mantém compatibilidade com funcionalidades existentes do tema

### 📋 Exemplo de Uso
```
Link 1: Nome: "Sintomas" | URL: "/sintomas"
Link 2: Nome: "Sobre Nós" | URL: "/sobre-nos"  
Link 3: Nome: "Blog" | URL: "https://blog.exemplo.com" | Nova aba: Sim
```

## [1.0.0] - 2024-01-08

### ✅ Inicial
- **Tema Base**: Versão original do MatchPlus
- **Estrutura**: Organização inicial do repositório
- **Backup**: Preservação da versão original do tema
- **Documentação**: README e estrutura de documentação

### 📁 Estrutura Criada
- `/original/` - Backup do tema original
- `/current/` - Versão com modificações
- `/docs/` - Documentação das modificações
- `README.md` - Documentação principal
- `CHANGELOG.md` - Este arquivo de mudanças

---

## 🔮 Próximas Versões Planejadas

### [1.2.0] - Futuro
- **Possíveis melhorias**:
  - Suporte a ícones nos links personalizados
  - Mais opções de posicionamento dos links
  - Integração com outras seções do tema

### 🔄 Atualizações do Tema Base
- **Quando disponível**: Atualização para nova versão do MatchPlus
- **Processo**: Preservação das modificações durante atualização
- **Compatibilidade**: Verificação e ajustes necessários

---

## 📝 Notas sobre Versionamento

- **Major (X.0.0)**: Mudanças incompatíveis ou grandes reestruturações
- **Minor (1.X.0)**: Novas funcionalidades mantendo compatibilidade
- **Patch (1.1.X)**: Correções de bugs e pequenos ajustes

## 🏷️ Tags e Releases

Cada versão será marcada com uma tag Git para facilitar o controle:
```bash
git tag -a v1.1.0 -m "Adiciona links personalizados no menu"
git push origin v1.1.0
```

---

**Mantido por**: Equipe Essential Energy  
**Tema Base**: MatchPlus by Samá Themes  
**Plataforma**: Tray Commerce
