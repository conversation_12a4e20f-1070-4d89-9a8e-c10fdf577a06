{% set link_classes = {
    'description'     : 'description-link-tab',
    'included_items'  : 'included-items-link-tab',
    'warranty'        : 'warranty-link-tab',
    'payment_methods' : 'payment-link-tab',
    'comments'        : 'comments-link-tab'
} %}

{% set tabs_classes = {
    'description'     : 'description-ab',
    'included_items'  : 'included-items-tab',
    'warranty'        : 'warranty-tab',
    'payment_methods' : 'payment-tab',
    'comments'        : 'comments-tab'
} %}

{% set tabs = [] %}

{% for key, tab in productTabs %}
    {% if key == 'custom' or key == 'downloads' or 'AdditionalTab' in key %}

        {% set tabs = tabs | merge({ (key) : tab }) %}

    {% elseif key == 'payment_methods' %}
        {% if product.available and (product.stock > 0 or settings.without_stock_sale) and not product.upon_request %}

            {% set currentTab = {
                'id'           : tab.id,
                'container_id' : tab.container_id,
                'description'  : tab.description,
                'link_class'   : link_classes[key] ? link_classes[key] : 'custom-link-tab',
                'tab_class'    : tabs_classes[key] ? tabs_classes[key] : 'custom-tab',
                'active_class' : loop.first ? 'active' : ''
            } %}

            {% if tab.url %}
                {% set currentTab = currentTab | merge({ 'url' : tab.url }) %}
            {% endif %}

            {% set tabs = tabs | merge({ (key) : currentTab }) %}

        {% endif %}
    {% else %}

        {% set currentTab = {
            'id'           : tab.id,
            'container_id' : tab.container_id,
            'description'  : tab.description,
            'link_class'   : link_classes[key] ? link_classes[key] : 'custom-link-tab',
            'tab_class'    : tabs_classes[key] ? tabs_classes[key] : 'custom-tab',
            'active_class' : loop.first ? 'active' : ''
        } %}

        {% if tab.url %}
            {% set currentTab = currentTab | merge({ 'url' : tab.url }) %}
        {% endif %}

        {% set tabs = tabs | merge({ (key) : currentTab }) %}

    {% endif %}
{% endfor %}

<ul class="tabs-nav">
    {% for key, tab in tabs %}
        {% if tab.container_id %}

            <li class="tab">
                <a class="tab-link {{ tab.active_class }} {{ tab.link_class }}" href="{{ tab.container_id }}">
                    <span class="text">
                        {{ tab.description }}
                    </span>
                </a>
            </li>

        {% endif %}
    {% endfor %}
</ul>


<div class="tabs-content">
    {% for key, tab in tabs %}

        {# Mobile tab links #}
        {% if tab.container_id %}
            <a class="tab-link-mobile {{ tab.link_class }} {{ tab.active_class }}" href="{{ tab.container_id }}">
                <span class="text">
                    {{ tab.description }}
                </span>
            </a>
        {% endif %}

        {# Tab content #}
        {% set tab_id = tab.container_id | replace({ '#' : '' }) %}

        {% if key != 'custom' and 'AdditionalTab' not in key %}

            {% set rte_class = (key == 'description' or key == 'warranty' or key == 'included_items' or key == 'datasheet') ? 'rte' : '' %}

            <div class="tab {{ rte_class }} {{ tab.tab_class }} {{ tab.active_class }}" id="{{ tab_id }}" {% if key == 'payment_methods' %} data-loaded-price="" {% endif %} {% if tab.url %} data-url="{{ tab.url }}" {% endif %}>

                {% if key == 'description' %}

                    {{ product.description|escape('html') }}

                {% elseif key == 'included_items' %}

                    Itens inclusos: {{ product.included_items }}

                {% elseif key == 'comments' %}

                    {%- set productComments -%}
                        {%- element 'product/comments' -%}
                    {%- endset -%}

                    <div id="tray-comment-block"></div>
                    <script> window.commentsBlock = `{{ productComments | raw }}`; </script>

                {% elseif key == 'downloads' %}

                    {{ tab.content }}

                {% elseif key == 'properties' %}

                    {% element 'product/properties' %}

                {% endif %}

            </div>

        {% elseif key == 'custom' %}
            {{ tab.content | replace({ 'prodBox' : 'tab rte custom-tab' }) }}
        {% endif %}

    {% endfor %}

</div>