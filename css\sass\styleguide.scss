// style guide for page extra and product descriptions
div[id^="AbaPersonalizada"] > .board > br:first-child{
    display: none;
}

.board_htm.description,
div[id^="AbaPersonalizada"]{
    
    h1{
        font-size: 4rem;
    }
    
    h2{
        font-size: 3.5rem;
    }
    
    h3{
        font-size: 3rem;
    }
    
    h4{
        font-size: 2.5rem;
    }
    
    h5{
        font-size: 2rem;
    }
    
    h6{
        font-size: 1.5rem;
    }
    
    p{
        margin: 1em 0;
    }
    
    a{
        color: #337ab7;
    }
    
    pre{
        background: #ccc;
        display: block;
        font-family: monospace;
        margin: 1em 0px 1em;
        padding: 10px;
        white-space: pre;
    }
    
    ol{
        list-style-type: decimal;
        margin: 1em 0;
        padding-left: 25px;
        
        li{
            margin: 0;
        }
    }
    
    ul{
        list-style-type: disc;
        margin: 1em 0;
        padding-left: 25px;
        
        li{
            margin: 0;
        }
    }
    
    blockquote{
        border-left: 5px solid #ddd;
        font-style: italic;
        overflow: hidden;
        padding-left: 25px;
    }
    
    input{
        
        &[type="checkbox"]{
            margin-right: 5px;
        }
        
        &[type="radio"]{
            margin-right: 5px;
        }
        
        &[type="text"]{
            border: 1px solid #ddd;
            color: #a2a2a2;
            padding: 8px 10px;
            @include typo(1.3rem, 400, $font, 1.3rem);
        }
        
        &[type="button"]{
            background: #ddd;
            border: 1px solid #ddd;
            color: #000;
            padding: 8px 10px;
        }
    }
    
    textarea{
        border: 1px solid #ddd;
        color: #a2a2a2;
        padding: 8px 10px;
        @include typo(1.3rem, 400, $font, 1.3rem);
    }
    
    select{
        border: 1px solid #ddd;
        color: #a2a2a2;
        padding: 8px 10px;
        @include typo(1.3rem, 400, $font, 1.3rem);
    }
    
    table{
        
        th{
            border-bottom: 2px solid #ddd;
            font-size: 14px;
            padding: 8px;
            vertical-align: bottom;
        }
        
        td{
            border-top: 1px solid #ddd;
            font-size: 14px;
            padding: 8px;
            vertical-align: top;
        }
    }
    
    iframe{
        
        @media (max-width: 767px){
            width: auto!important;
            height: auto!important;
        }
    }
}