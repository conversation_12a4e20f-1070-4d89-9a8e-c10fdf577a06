/**
 * Search
 */

.search{
    
    @media screen and (max-width: $sm){
        padding: 10px 0;
    }
    
    form{
        position: relative;
        background: #fff;
        height: 55px;
        
        > div{
            overflow: hidden;
        }
    }
    
    .search-button{
        background: #a2a2a2;
        border: none;
        border-right: none;
        height: 55px;
        padding: 0 11px;
    }
    
    .search-key{
        background: none;
        border: 1px solid #e2e2e2;
        border-right: none;
        font-size: 1.3rem;
        height: 55px;
        padding: 19px 25px;
        width: 100%;
        
        @media screen and (max-width: $sm){
            border: 1px solid #a2a2a2;
            border-right: none;
            font-size: 1.6rem;
            height: 55px;
            padding: 17px 25px;
        }
    }
}