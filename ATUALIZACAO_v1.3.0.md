# 🚀 Atualização v1.3.0 - Links Personalizados com Controle de Nova Aba

## 📋 Resumo das Mudanças

### ✅ **Principais Melhorias:**
1. **12 links personalizados** com controle individual
2. **Restaurada opção "abrir em nova aba"** - cada link pode ser configurado independentemente
3. **Interface aprimorada** - controle de nova aba para cada link
4. **Documentação atualizada** com exemplos práticos

### 🎯 **Benefícios:**
- **Máxima flexibilidade**: 12 links + controle individual de abertura
- **Experiência otimizada**: Links internos na mesma aba, externos em nova aba
- **Interface completa**: Cada link tem sua própria configuração
- **Compatibilidade total**: Funciona perfeitamente em desktop e mobile

## 📁 **Arquivos Modificados:**

| Arquivo | Modificação | Descrição |
|---------|-------------|-----------|
| `configs/settings.html` | Expandido | 12 links + opção de nova aba para cada |
| `elements/snippets/menu.html` | Loop atualizado | `{% for i in 1..12 %}` + `target="_blank"` |
| `elements/snippets/menu-mobile.html` | Loop atualizado | `{% for i in 1..12 %}` + `target="_blank"` |
| `configs/settings.json` | Campos adicionados | Todos os 12 campos + opções de nova aba |
| `docs/LINKS_PERSONALIZADOS_MENU.md` | Documentação | Atualizada com novos exemplos |
| `README.md` | Informações | Atualizadas para controle de nova aba |
| `CHANGELOG.md` | Histórico | Nova versão v1.3.0 documentada |

## 🔄 **Para Atualizar no GitHub:**

### **1. Baixar Arquivos Atualizados**
Baixe todos os arquivos modificados desta pasta.

### **2. Substituir no Repositório**
Substitua os arquivos correspondentes na pasta `current/` do seu repositório.

### **3. Commit Sugerido**
```bash
git add .
git commit -m "feat: adiciona controle de nova aba para todos os 12 links personalizados

✨ Melhorias:
- Restaurada opção 'abrir em nova aba' para todos os 12 links
- Controle individual para cada link (mesma aba ou nova aba)
- Interface aprimorada com configuração separada
- Máxima flexibilidade para experiência do usuário

📝 Arquivos modificados:
- configs/settings.html - Opção nova aba para cada link
- elements/snippets/menu.html - target='_blank' condicional
- elements/snippets/menu-mobile.html - target='_blank' condicional
- configs/settings.json - Campos menu_custom_link_blank_X
- Documentação atualizada

🎯 Resultado: Controle total sobre comportamento dos links"
```

### **4. Criar Tag da Nova Versão**
```bash
git tag -a v1.3.0 -m "Versão 1.3.0 - 12 links personalizados com controle de nova aba"
git push origin v1.3.0
```

## 💡 **Como Usar a Nova Versão:**

### **No Painel Administrativo:**
1. Acesse: **Personalizar Tema** → **"Links Personalizados do Menu"**
2. Configure até 12 links (cada um com sua própria seção)
3. Para cada link configure: **Nome**, **URL** e **Abrir em nova aba**

### **Exemplos de Configuração:**
```
Link 1: Nome: "Sintomas" | URL: "/sintomas" | Nova aba: Não
Link 2: Nome: "Sobre Nós" | URL: "/sobre-nos" | Nova aba: Não
Link 3: Nome: "Blog" | URL: "https://blog.exemplo.com" | Nova aba: Sim
Link 4: Nome: "Tratamentos" | URL: "/tratamentos" | Nova aba: Não
...
Link 12: Nome: "Contato" | URL: "/contato" | Nova aba: Não
```

## ⚠️ **Observações Importantes:**

### **Compatibilidade:**
- ✅ **Totalmente compatível** com configurações existentes
- ✅ **Links já configurados** continuarão funcionando
- ✅ **Sem quebra de funcionalidade**

### **Comportamento:**
- 🔗 **Controle individual**: Cada link pode abrir na mesma aba ou em nova aba
- 📱 **Funciona em desktop e mobile**
- 🎨 **Mantém estilização do tema**
- 🎯 **Experiência otimizada**: Links internos na mesma aba, externos em nova aba

### **Flexibilidade:**
- 📊 **Use quantos links precisar** (não precisa usar todos os 12)
- 🔄 **Links vazios não aparecem** no menu
- 📝 **Fácil configuração** no painel admin
- ⚙️ **Controle total** sobre abertura de cada link

## 🧪 **Testes Realizados:**

- ✅ Menu desktop - 12 links funcionando
- ✅ Menu mobile - 12 links funcionando  
- ✅ Controle de nova aba funcionando
- ✅ Interface do painel admin completa
- ✅ Compatibilidade com configurações existentes
- ✅ Responsividade mantida
- ✅ Estilização integrada

## 📞 **Suporte:**

Se tiver alguma dúvida sobre a atualização:
1. Consulte a documentação em `docs/LINKS_PERSONALIZADOS_MENU.md`
2. Verifique o `CHANGELOG.md` para detalhes técnicos
3. Abra uma issue no repositório GitHub

---

**Versão**: 1.3.0  
**Data**: Janeiro 2024  
**Status**: ✅ Testado e funcional  
**Compatibilidade**: Mantida com versão anterior
