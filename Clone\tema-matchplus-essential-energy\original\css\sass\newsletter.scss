/**
 * Newsletter
 */
 
.news-full {
    background: #fff;
    margin-top: 30px;
    position: relative;
    width: 100%;
}

.newsletter{
    border: 1px solid #e2e2e2;
    padding: 15px;
    
    h2{
        color: #000;
        margin-bottom: 15px;
        text-transform: uppercase;
        overflow: hidden;
        padding-bottom: 0;
        @include typo(2.2rem, 400, $font, 2.4rem);
        
        @media (min-width: 992px) and (max-width: 1200px){
            font-size: 1.9rem;
        }
        
        @media (min-width: 768px) and (max-width: 991px){
            font-size: 1rem;
        }
        
        span{
            display: inline-block;
            line-height: 35px;
            vertical-align: top;
        }
    }
    
    i{
        display: inline-block;
        height: 35px;
        margin: 0 10px 0 0;
        vertical-align: top;
        text-align: center;
        width: 35px;
        
        @media (min-width: 768px) and (max-width: 991px){
            height: 30px;
            width: 30px;
        }
    }
    
    form{
            
        input{
            background: none;
            border: 1px solid #e2e2e2;
            color: #a2a2a2;
            display: block;
            margin-bottom: 15px;
            padding: 8px 10px;
            width: 100%;
            @include typo(1.3rem, 400, $font, 1.3rem);
        }
        
        button{
            background: #9e9e9e;
            border: none;
            color: #fff;
            display: block;
            padding: 0;
            text-align: center;
            text-transform: uppercase;
            width: 100%;
            @include typo(1.5rem, 400, $font, 3.5rem);
        }
    }
}