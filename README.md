# Tema MatchPlus - Essential Energy

Tema MatchPlus customizado com funcionalidades adicionais para a loja Essential Energy.

## 🚀 Modificações Implementadas

### ✅ Links Personalizados no Menu Superior
- **Data**: Janeiro 2024
- **Descrição**: Adiciona até 3 links personalizados no menu superior
- **Localização**: Menu desktop e mobile
- **Configuração**: Painel administrativo → Personalizar Tema

## 📁 Estrutura do Repositório

```
tema-matchplus-essential-energy/
├── README.md                    # Este arquivo
├── CHANGELOG.md                 # Histórico de mudanças
├── original/                    # Tema original (backup)
│   ├── configs/
│   ├── css/
│   ├── elements/
│   ├── img/
│   ├── js/
│   ├── layouts/
│   └── pages/
├── current/                     # Versão atual com modificações
│   ├── configs/
│   ├── css/
│   ├── elements/
│   ├── img/
│   ├── js/
│   ├── layouts/
│   └── pages/
└── docs/                        # Documentação
    └── LINKS_PERSONALIZADOS_MENU.md
```

## 🛠️ Como Usar as Modificações

### Links Personalizados no Menu
1. Acesse: **Painel Admin** → **Configurações** → **Personalizar Tema**
2. Procure pela seção: **"Links Personalizados do Menu"**
3. Configure até 3 links:
   - **Nome do Link**: Texto que aparecerá no menu
   - **URL do Link**: Endereço de destino
   - **Abrir em nova aba**: Opcional

### Exemplo de Configuração
- **Link 1**: Nome: `Sintomas` | URL: `/sintomas`
- **Link 2**: Nome: `Sobre Nós` | URL: `/sobre-nos`
- **Link 3**: Nome: `Blog` | URL: `https://blog.exemplo.com` | Nova aba: ✅

## 📋 Arquivos Modificados

| Arquivo | Descrição | Modificação |
|---------|-----------|-------------|
| `configs/settings.html` | Interface de configuração | Adicionada seção "Links Personalizados do Menu" |
| `elements/snippets/menu.html` | Menu desktop | Implementada lógica para exibir links personalizados |
| `elements/snippets/menu-mobile.html` | Menu mobile | Implementada lógica para exibir links personalizados |
| `css/custom.css.html` | Estilos CSS | Adicionados estilos para os links personalizados |
| `configs/settings.json` | Configurações | Adicionado exemplo de configuração |

## 🔄 Processo de Atualização do Tema

### Quando uma nova versão do tema for lançada:

1. **Backup da versão atual**:
   ```bash
   git checkout -b backup-v1.1.0
   git checkout main
   ```

2. **Atualizar tema original**:
   - Baixar nova versão do tema
   - Substituir conteúdo da pasta `/original/`

3. **Comparar mudanças**:
   ```bash
   git diff original/ current/
   ```

4. **Aplicar modificações**:
   - Replicar customizações na nova versão
   - Testar funcionalidades

5. **Commit da nova versão**:
   ```bash
   git add .
   git commit -m "feat: atualiza tema para versão X.X.X com modificações preservadas"
   ```

## 📖 Documentação Detalhada

Para instruções detalhadas sobre cada modificação, consulte:
- [`docs/LINKS_PERSONALIZADOS_MENU.md`](docs/LINKS_PERSONALIZADOS_MENU.md)

## 🏪 Sobre a Loja

**Essential Energy**
- URL: https://essentialenergy.commercesuite.com.br/
- Tema: MatchPlus (Samá Themes)
- Plataforma: Tray Commerce

## 📝 Notas Importantes

- Este repositório contém modificações de um tema comercial
- Mantenha sempre backup da versão original
- Teste todas as modificações em ambiente de desenvolvimento
- As modificações são compatíveis com a estrutura atual do tema MatchPlus

## 🤝 Contribuições

Para sugerir melhorias ou reportar problemas:
1. Abra uma **Issue** descrevendo o problema/sugestão
2. Para modificações, crie um **Pull Request** com descrição detalhada

---

**Última atualização**: Janeiro 2024  
**Versão do tema base**: MatchPlus (versão atual instalada)  
**Status**: ✅ Funcional e testado
