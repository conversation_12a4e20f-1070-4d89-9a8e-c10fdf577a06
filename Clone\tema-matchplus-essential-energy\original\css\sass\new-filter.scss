.new-smart-filter{
    background: #fff;
    padding: 10%;
    border: 1px solid #e2e2e2;
    margin-top: 30px;

    .filter{
        &__block{
            margin: 10% 0;
            &--properties{
                overflow:auto;
                max-height: 385px;

                &::-webkit-scrollbar{
                    background: transparent;
                    width: 10px;
                }

                &::-webkit-scrollbar-thumb{
                    background-color: #ccc;
                }
            }
        }

        &__title{
            font-size: 1.5rem;
            padding: 5% 0;
        }

        &__list{
            font-size: 1.3rem;
        }

        &__label{
            padding-left: 5%;
        }

        &__text{
            &--count{
                font-size: 1rem;
                color: #a2a2a2;
                padding: 5px;
            }
        }

        &__button{
            width: 100%;
            padding: 10px;
            border: none;
            font-size: 1.7rem;
            background-color: #525252;
            color: #fff;
        }

        &__name{
            img{
                border-radius: 19px;
                margin: 5px 0;
            }
        }
    }

    .open-filters{
        color: #000;
        margin-bottom: 15px;
        text-transform: uppercase;
        overflow: hidden;
        padding-bottom: 0;
        font-family: "<PERSON>o",sans-serif;
        font-size: 2.2rem;
        font-weight: 400;
        line-height: 2.4rem;
    }
}
