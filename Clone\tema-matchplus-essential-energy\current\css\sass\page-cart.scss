/**
 * Cart
 */

// tabs
.carrinho-tabs{
    background: #fff;
    border: 1px solid #e2e2e2;
    margin: 0 0 30px;
    padding: 20px 0;
        
    ol{
        text-align: center;
    }
    
    li{
        display: inline-block;
        margin-left: 50px;
        opacity: 0.5;
        @include prefix('filter', 'grayscale(100%)');
        
        @media screen and (max-width: $sm){
            display: none;
            
            &.active{
                display: block;
            }
        }
        
        @media (min-width: 768px) and (max-width: 1200px){
            margin-left: 30px;
        }
        
        &.active{
            opacity: 1;
            @include prefix('filter', 'none');
            filter: none;
        }
        
        &:first-child{
            margin-left: 0;
        }
    }
    
    .passo-desc{
        display: none;
    }
    
    .passo-num{
        color: #000;
        display: inline;
        font-size: 14px;
        font-weight: 400;
        
        @media (min-width: 768px) and (max-width: 1200px){
            font-size: 10px;
        }
        
        &:after{
            content: '.';
            display: inline-block;
            vertical-align: top;
        }
    }
    
    .passo-text{
        color: #000;
        display: inline;
        font-size: 14px;
        font-weight: 400;
        
        @media (min-width: 768px) and (max-width: 1200px){
            font-size: 10px;
        }
    }
}

// page
.page-checkout_cart{
    
    .container.caixa-produto + .container,
    .Seguro,
    .AvancarTopo,
    .AvancarTopo + .botao-prosseguir-compra{
        display: none;
    }
    
    .AvancarTopo + .bt-avancar,
    .bt-continuar-comprando + .bt-avancar{
        line-height: 0;
        margin-bottom: 20px;
        
        @media screen and (max-width: $sm){
            display: block;
            float: none;
            text-align: center;
        }
    }
    
    .light_altura{
        
        p{
            font-size: 2rem;
            line-height: 2.4rem;
            margin-top: 15px;
        }
    }
    
    .caixa-produto{
        padding: 0;
        width: 100%;
    }
    
    #cesta_produtos{
        margin: 0;
        
        > .board:first-child{
            font-size: 0;
        }
    }
    
    #tabela_carrinho{
        background: #fff;
        border: 1px solid #e2e2e2;
        border-radius: 3px 3px 3px 3px;
        margin: 0;
        
        @media screen and (max-width: $sm){
            background: none;
        }
        
        tr{
            background: #fff;
            position: relative;
            
            @media screen and (max-width: $sm){
                display: block;
                margin-bottom: 15px;
                padding: 15px;
                
                &:first-child{
                    display: none;
                }
            }
            
            &:last-child td{
                border-bottom: none;
            }
        }
        
        th{
            border-bottom: 1px solid #eee;
            font-size: 14px;
            padding: 10px 30px;
            vertical-align: top;
            white-space: nowrap;
            
            @media screen and (max-device-width: 1024px){
                padding: 0 15px;
            }
            
            @media (min-width: 768px) and (max-width: 991px){
                padding: 10px 0;
            }
            
            &:first-child{
                padding-left: 94px;
                padding-right: 40px;
            }
            
            &:last-child{
                padding-left: 32px;
                padding-right: 10px;
                
                @media (min-width: 768px) and (max-width: 991px){
                    padding-left: 0;
                }
            }
        }
        
        td{
            border-top: none;
            border-bottom: solid 1px #e8e8e8;
            padding: 20px 0;
            
            &:first-child{
                padding: 0;
                vertical-align: middle;
                
                a{
                    display: block;
                    padding: 0 5px 5px;
                }
                
                img{
                    border: none;
                    max-height: 100%;
                    max-width: 100%;
                }
            }
            
            &:nth-child(2) ~ td{
                text-align: center;
            }
            
            @media screen and (max-width: $sm){
                border-bottom: none;
                border-top: none;
                display: block;
                padding: 0;
                text-align: left;
                
                &:nth-child(1){
                    float: left;
                    padding: 0 0 10px;
                    text-align: center;
                    width: 40%;
                }
                
                &:nth-child(2){
                    margin-bottom: 20px;
                    overflow: hidden;
                }
                
                &:nth-child(3){
                    clear: left;
                    float: left;
                    width: 40%;
                }
                
                &:nth-child(4){
                    float: left;
                    text-align: left;
                    width: 60%;
                    
                    &:before{
                        content: 'Valor uni.';
                    }
                }
                
                &:nth-child(5){
                    float: left;
                    text-align: left;
                    width: 60%;
                    
                    h3{
                        display: inline;
                    }
                    
                    &:before{
                        content: 'Valor total';
                    }
                }
                
                &:last-child{
                    clear: both;
                }
            }
        }
        
        a strong{
            color: #3d4445;
            font-size: 14px;
            font-weight: 700;
            line-height: 20px;
            margin: 0;
            padding: 0;
            text-transform: none;
        }
        
        h5{
            color: #3d4445;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            margin: 0;
            padding: 0;
            text-transform: none;
        }
        
        .qntd{
            border: solid 1px #bcc0bd;
            font-weight: normal;
            height: 30px;
            text-align: center;
            width: 38px;
        }
        
        .refresh{
            background: url("../img/reload.svg?1") no-repeat center top;
            cursor: pointer;
            display: block;
            height: 20px;
            margin: 5px auto 0;
            width: 20px;
            
            &:after{
                display: none;
            }
            
            img{
                display: none;
            }
        }
        
        .bt-excluir{
            background: url('../img/close.svg?1') no-repeat center top;
            -webkit-background-size: 10px;
            background-size: 10px;
            cursor: pointer;
            display: block;
            height: 12px;
            margin: 2px 0 0;
            width: 12px;
            
            @media screen and (max-width: $sm){
                background-color: #fff;
                background-position: center;
                border-radius: 10px;
                border: 1px solid #3D4445;
                padding: 10px;
                position: absolute;
                right: -5px;
                top: -5px;
            }
            
            img{
                display: none;
            }
            
            @media screen and (max-width: $sm){
                margin: 0 auto;
            }
        }
        
        .valores_carrinho{
            color: #3d4445 !important;
            font-size: 14px;
            font-weight: 400;
            margin: 0;
        }
    }
    
    div.bt-continuar-comprando{
        
        @media screen and (max-width: $sm){
            display: block;
            margin-bottom: 20px;
            position: static;
            right: 0;
            text-align: center;
            width: 100%;
        }
    
        a{
            float: none;
            font-size: 14px;
        }
    }
    
    .caixa-frete{
        display: none;
    }
    
    .caixa-forma-frete{
        margin-top: 20px;
        background-color: #fff;
        
        tr{
            display: block;
            overflow: hidden;
            
            &:nth-child(3){
                th, span{
                    font-size: 16px;
                }
            }
            
            td{
                h3 { display: none; }
            }
        }
        
        th{
            display: block;
        }
        
        input[type="tel"]{
            border: solid 1px #bcc0bd;
            color: #3d4445;
            font-size: 1.5rem;
            font-weight: 400;
            height: 30px;
            padding: 0 10px;
            text-align: center;
            
            @media screen and (max-width: $sm){
                padding: 0;
            }
        }
        
        .bt-cep{
            margin-top: 4px;
            
            @media screen and (max-width: $sm){
                display: inline-block;
            }
        }
        
        .botao-simular-frete{
            display: inline-block;
            margin-left: 10px;
            padding: 9px;
            vertical-align: top;
            
            @media screen and (max-width: 480px){
                margin-left: 0;
                padding: 9px 5px;
            }
        }
        
        .carFretePara{
            display: block;
            padding: 10px 0;
        }
        
        #formas_envio_frete{
            display: block;
            overflow: hidden;
            padding-left: 30px;
            
            @media screen and (max-device-width: 1024px){
                padding-left: 0;
                width: 100%;
            }
        }
        
        #calculoFrete{
            
            label{
                color: #3d4445;
                display: inline-block;
                font-size: 13px;
                line-height: 14px;
                vertical-align: middle;
            }
            
            @media screen and (max-width: $sm){
                input{
                    font-size: 1.6rem;
                    margin: 0 3px;
                    padding: 0 3px;
                    width: 70px;
                    
                    &#cep2{
                        width: 40px;
                    }
                }
            }
        }
        
        .blocoAlerta{
            margin: 20px 0;
        }
        
        .caixa-cupom{
            
            label{
                color: #3d4445;
                display: inline-block;
                font-size: 13px;
                line-height: 14px;
                vertical-align: middle;
            }
            
            .botao-cupom-desconto{
                display: inline-block;
                margin: 0 10px;
                padding: 9px;
                vertical-align: top;
            }
            
            .blocoSucesso{
                margin: 0 0 15px;
            }
        }
        
        #tab{

            td{
                color: #3d4445;
                font-size: 14px;
                line-height: 14px;
                padding: 5px;
                vertical-align: top;
                font-style: normal;
                
                &:first-child{
                    min-width: 150px;
                }
                
                &:first-child,
                &:nth-child(2),
                &:nth-child(3){
                    width: auto;
                    
                    @media screen and (max-width: $sm){
                        display: block;
                        padding-left: 15px;
                        text-align: left;
                    }
                }
                
                &:nth-child(2){
                    text-align: left;
                    width: 140px;
                }
                
                &:nth-child(3){
                    margin-top: 0;
                    
                    @media screen and (max-device-width: 1024px){
                        display: none;
                    }
                }
            }
            
            a{
                color: #3d4445;
            }
            
            label{
                color: #3d4445;
                // display: inline-block;
                font-size: 14px;
                vertical-align: top;
            }
        }
    }
    
    .frete-wrapper,
    .cupom-wrapper{
        clear: both;
        float: left;
        text-align: left;
        
        @media screen and (max-width: $sm){
            float: none;
            width: 100%;
        }
    }
    
    .cupom-wrapper{
        background: #fff;
        border: 1px solid #e2e2e2;
        margin-top: 30px;
        padding: 15px 30px;
        width: 100%;
        
        @media screen and (max-width: $sm){
            padding: 15px;
        }
        
        th{
            float: left;
            text-align: left;
            
            &:nth-child(2){
                line-height: 30px;
            }
        }
        
        @media screen and (max-width: $sm){
            
            th{
                
                &:nth-child(2){
                    line-height: 12px;
                    padding: 30px;
                    padding-right: 0;
                }
            }
            
            .botao-cupom-desconto{
                margin: 0 !important;
                width: 100%;
            }
            
            #cupon{
                display: block;
                font-size: 1.6rem;
                margin: 5px 0 10px;
                width: 100%;
            }
        }
        
        #cupon{
            border: solid 1px #bcc0bd;
            color: #3d4445;
            font-size: 1.5rem;
            font-weight: 400;
            height: 30px;
            padding: 0 10px;
            text-align: center;
            
            @media screen and (max-width: $sm){
                padding: 0;
            }
        }
        
        input[type="tel"]{
            border: solid 1px #bcc0bd;
            color: #3d4445;
            font-size: 1.5rem;
            font-weight: 400;
            height: 30px;
            padding: 0 10px;
            text-align: center;
            
            @media screen and (max-width: $sm){
                padding: 0;
            }
        }
        
        @media screen and (max-width: 404px){            
            .botao-commerce{
                margin-top: 15px;
            }
        }
    }
    
    .margem_imagem_frete{
        display: block;
    }
    
    .frete-wrapper{
        padding: 0 30px;
        
        @media screen and (max-width: 404px){            
            padding: 0;
        }
        
        + tr{
            clear: both;
            
            > td > h3{
                display: none;
            }
        }
    }
    
    .caixa-total{
        margin-bottom: 20px;
        background-color: #fff;
        
        .tit-total{
            display: none;
        }
        
        table{
            margin: 0;
        }
        
        tr{
            background: none;
            border: none;
        }
        
        th{
            border: none !important;
            font-size: 0;
            padding: 0 !important;
            
            &:nth-child(2){
                text-align: right;
                width: 50%;
            }
            
            &:last-child{
                width: 1px;
            }
        }
        
        h3{
            color: #3d4445 !important;
            font-size: 30px;
            font-weight: 400;
            white-space: nowrap;
            
            div{
                color: #3d4445 !important;
                font-weight: 400;
            }
            
            &:before{
                color: #3d4445 !important; 
                content: 'Total: ';
                text-transform: none;
            }
        }
    }
    
    .caixa-botoes{
        margin-bottom: 30px;
        
        .botao-commerce{
            font-size: 1.4rem;
            padding: 9px 25px;
            
            @media screen and (max-width: $sm){
                text-align: center;
                width: 100%;
            }
            
            &.botao-prosseguir-compra{
                background: #3d4445;
                border: none;
                color: #fff;
                height: 57px;
                padding: 18px 0;
                text-align: center;
                width: 200px;
                @include typo(2rem, 400, $font, 2rem);
                
                @media screen and (max-width: $sm){
                    text-align: center;
                    width: 100%;
                }
                
                .botao-commerce-img {
                    color: #fff !important;
                }
            }
            
            &.botao-continuar-comprando{
                background: #ccc;
            }
        }
    }
    
    
    // cart without items
    .Seguro + .board{
        background: #fff;
        font-size: 1rem;
        padding: 30px 0 15px;
        text-align: center;
        
        h2{
            
            &:before{
                background: url('../img/cart-empty.svg');
                background-size: 100%;
                content: '';
                display: block;
                height: 100px;
                margin: 0 auto 15px;
                width: 100px;
            }
        }
        
        br{
            display: none;
        }
        
        a{
            background: #a2a2a2;
            color: #fff;
            display: inline-block;
            font-size: 1.5rem;
            margin: 15px;
            padding: 15px;
        }
    }
    
    
    // resource gift list
    .bloco-lista{
        background: #fff;
        margin-bottom: 30px;
        padding: 15px;
    }
    
    .imagem-alerta-lista{
        float: left;
        margin-right: 15px;
        
        &:before{
            background: url('../img/gift.svg');
            content: '';
            display: block;
            height: 50px;
            width: 50px;
        }
        
        img{
            display: none;
        }
    }
    
    .msg-alerta-lista{
        overflow: hidden;
        font-size: 1.2rem;
        
        strong{
            font-size: 1.2rem !important;
            
            u{
                color: red;
            }
        }
        
        #atencao-lista{
            font-weight: 700;
        }
    }
    
    .imagem-sair-lista{
        margin-top: 15px;
        text-align: center;
    }
}