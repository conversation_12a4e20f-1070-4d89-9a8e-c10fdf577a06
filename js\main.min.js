(function($){$.fn.changeElementType=function(e){var t={};$.each(this[0].attributes,(function(e,a){t[a.nodeName]=a.nodeValue})),this.replaceWith((function(){return $("<"+e+"/>",t).append($(this).contents())}))},Number.prototype.formatMoney=function(e=2,t=".",a=",",o=!1){let i=this.toFixed(e).split("."),s=i[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g,`$1${a}`)+(i[1]?t+i[1]:"");return o?"R$ {{amount}}".replace(/{{\s*(\w+)\s*}}/,s):s},window.theme={...window.theme,settings:{lastScrollPosition:0,storeId:0,productVariantsQuantities:null,productThumbs:null,productGallery:null},recoveryStoreId:function(){this.settings.storeId=$("html").data("store")},resets:function(){let e=$(".logotray-message a");e.attr("rel","noopener").attr("href",e.attr("href").replace("http","https")),$('[role="dialog"] .modal-title').removeAttr("id"),$('.page-search #Vitrine input[type="image"]').after('<button type="submit" class="botao-commerce">BUSCAR</button>'),$('.page-search #Vitrine input[type="image"]').remove(),$(".advancedSearchFormBTimg").addClass("botao-commerce"),$('.page-central_senha input[type="image"]').after('<button type="submit" class="botao-commerce">CONTINUAR</button>').remove(),$(".caixa-cadastro #email_cadastro").attr("placeholder","Seu e-mail"),$('#imagem[src*="filtrar.gif"]').after('<button type="submit" class="botao-commerce">Filtrar</button>'),$('#imagem[src*="filtrar.gif"]').remove(),$('input[src*="gerarordem.png"]').after('<button type="submit" class="botao-commerce">Gerar ordem de devolu&ccedil;&atilde;o</button>'),$('input[src*="gerarordem.png"]').remove()},initMasks:function(){let e=function(e){return 11===e.replace(/\D/g,"").length?"(00) 00000-0000":"(00) 0000-00009"},t={onKeyPress:function(t,a,o,i){o.mask(e.apply({},arguments),i)}};$(".phone-mask").mask(e,t),$(".zip-code-mask").mask("00000-000")},initLazyload:function(e=".lazyload"){new LazyLoad({elements_selector:e})},getLoader:function(e=null){return`\n                <div class="loader show">\n                    <div class="spinner">\n                        <div class="double-bounce-one"></div>\n                        <div class="double-bounce-two"></div>\n                    </div>\n                    ${e?`<div class="message">${e}</div>`:""}\n                </div>`},scrollToElement:function(e,t=0){e&&"#"!==e&&$("html,body").animate({scrollTop:Math.round($(e).offset().top)-t},600)},overlay:function(){$('[data-toggle="overlay-shadow"]').on("click",(function(){$($(this).data("target")).addClass("show").attr("data-overlay-shadow-target",""),$(".overlay-shadow").addClass("show"),$("body").addClass("overflowed")})),$(".overlay-shadow").on("click",(function(){$("[data-overlay-shadow-target]").removeClass("show").removeAttr("data-overlay-shadow-target"),$(".overlay-shadow").removeClass("show"),$("body").removeClass("overflowed")})),$(".close-overlay").on("click",(function(){$(".overlay-shadow").trigger("click")}))},toggleModalTheme:function(){$("body").on("click",'[data-toggle="modal-theme"]',(function(){$($(this).data("target")).addClass("show")})),$(".modal-theme:not(.no-action) .modal-shadow, .modal-theme:not(.no-action) .close-icon").on("click",(function(){$(".modal-theme").removeClass("show")}))},generateBreadcrumb:function(e=""){let t,a="",o=document.title.split(" - ")[0];t="news-page"==e?[{text:"Home",link:"/"},{text:"Not&iacute;cias",link:"/noticias"},{text:o}]:[{text:"Home",link:"/"},{text:o}],$.each(t,(function(e,t){this.link?a+=`\n                        <li class="breadcrumb-item flex align-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">\n                            <a itemprop="item" class="t-color" href="${t.link}">\n                                <span itemprop="name">${t.text}</span>\n                            </a>\n                            <meta itemprop="position" content="${e+1}" />\n                        </li>   \n                    `:a+=`\n                        <li class="breadcrumb-item flex align-center" itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">\n                            <span itemprop="name">${t.text}</span>\n                            <meta itemprop="position" content="${e+1}" />\n                        </li>          \n                    `})),$(".page-content > .container").prepend(`\n                <ol class="breadcrumb flex f-wrap" itemscope itemtype="https://schema.org/BreadcrumbList">\n                    ${a}\n                </ol>\n            `)},processRteElements:function(){$(".col-panel .tablePage, \n               .page-extra .page-content table, \n               .page-extras .page-content table, \n               .board_htm table,\n               .rte table,\n               .page-noticia table\n            ").wrap('<div class="table-overflow"></div>'),$('.page-noticia iframe[src*="youtube.com/embed"], \n               .page-noticia iframe[src*="player.vimeo"],\n               .board_htm iframe[src*="youtube.com/embed"],\n               .board_htm iframe[src*="player.vimeo"],\n               .rte iframe[src*="youtube.com/embed"],\n               .rte iframe[src*="player.vimeo"]\n            ').wrap('<div class="rte-video-wrapper"></div>')},setCorrectHeaderDesk:function(){let e=$(".header"),t=$(".header .nav"),a=2*$(".header").outerHeight(),o=$(window).scrollTop();o>0?e.addClass("fixed"):e.removeClass("fixed"),o>this.settings.lastScrollPosition||o<=a?t.removeClass("show-nav"):o>a&&t.addClass("show-nav"),this.settings.lastScrollPosition=o},scrollHeader:function(){let e=this;$(window).width()>=768&&this.setCorrectHeaderDesk(),$(window).on("scroll",(function(){$(window).width()>=768&&e.setCorrectHeaderDesk()}))},fixSubcategoriesHeight:function(){let e=$(".header").height(),t=$(window).height();$(".nav .list > .first-level.sub:not(.all-categories) .second-level").css("max-height",t-e-30)},mainMenu:function(){let e=this;this.fixSubcategoriesHeight(),$(window).on("resize",(function(){e.fixSubcategoriesHeight()}))},mainMenuMobile:function(){$(".nav-mobile .first-level > .sub > a").on("click",(function(e){let t=$(this).parent();return t.toggleClass("show"),t.hasClass("show")?t.children(".sub").slideDown():t.children(".sub").slideUp(),e.preventDefault(),!1}))},bannerHome:function(){if($(".banner-home").length){let e=$(".banner-home"),t=$(".swiper-slide",e).length,a=e.data("settings");t>0&&(new Swiper(".banner-home .swiper-container",{preloadImages:!1,loop:a.isLoop,autoHeight:!0,speed:.5==a.speed?500:1==a.speed?1e3:1.5==a.speed?1500:2==a.speed?2e3:0,effect:"slide",autoplay:{delay:0==a.isAutoplay?99999999:a.timer},lazy:{loadPrevNext:!0},navigation:{nextEl:".banner-home .next",prevEl:".banner-home .prev"},pagination:{el:".banner-home .dots",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!a.isMobile}}),a.stopOnHover&&($(".banner-home .swiper-container").on("mouseenter",(function(){this.swiper.autoplay.stop()})),$(".banner-home .swiper-container").on("mouseleave",(function(){this.swiper.autoplay.start()}))))}},storeReviewsIndex:function(){if($(".section-avaliacoes .dep_lista").length){$(".dep_lista").changeElementType("div"),$(".dep_item").changeElementType("div"),$(".dep_item").addClass("swiper-slide"),$(".section-avaliacoes .dep_lista").addClass("swiper-wrapper").wrap('<div class="swiper-container"></div>'),$(".section-avaliacoes .swiper-container").append('\n                    <div class="prev">\n                        <i class="icon icon-arrow-left"></i>\n                    </div>\n                    <div class="next">\n                        <i class="icon icon-arrow-right"></i>\n                    </div>            \n                    <div class="dots"></div>\n                ');new Swiper(".section-avaliacoes .swiper-container",{slidesPerView:3,centerInsufficientSlides:!0,lazy:{loadPrevNext:!0},navigation:{nextEl:".section-avaliacoes .next",prevEl:".section-avaliacoes .prev"},loop:!1,breakpoints:{0:{slidesPerView:1},600:{slidesPerView:2},1e3:{slidesPerView:3}},pagination:{el:".section-avaliacoes .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!1},on:{init:function(){$(".section-avaliacoes").addClass("show")}}});$(".section-avaliacoes .dep_dados").wrap('<a href="/depoimentos-de-clientes" title="Ver depoimento"></a>'),$(".dep_lista li:hidden").remove()}else $(".section-avaliacoes").remove()},loadNews:function(){if($(".section-news").length){let e=$("html").data("files");$.ajax({url:`/loja/busca_noticias.php?loja=${this.settings.storeId}&${e}`,method:"get",success:function(e){let t,a;$(e).find(".noticias").length?(t=$(".section-news .news-content .swiper-wrapper"),a=$($(e).find(".noticias")),a.find("li:nth-child(n+4)").remove(),a.find("li").wrapInner('<div class="swiper-slide"><div class="box-noticia"></div></div>'),a.find(".swiper-slide").unwrap(),a=a.contents(),a.each((function(e,t){$(t).find("img").closest("div").remove()})),t.append(a),new Swiper(".section-news .news-content",{lazy:{loadPrevNext:!0},pagination:{el:".news-content .dots",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!1},breakpoints:{0:{slidesPerView:1},550:{slidesPerView:2},768:{slidesPerView:3,allowTouchMove:!1}}})):$(".section-news").remove()}})}},slideCatalog:function(){$(".slide-catalog").length},sortMobile:function(){let e=$(),t=$("#select_ordem").val();$("#select_ordem option").each((function(){e=e.add(`<li ${t===$(this).val()?'class="active"':""} data-option="${$(this).val()}">\n                        ${$(this).text()}\n                    </li>\n                `)})),$(".catalog-header .sort-mobile .sort-panel .sort-options").append(e),$(".catalog-header .sort-mobile .sort-panel .sort-options").on("click","li",(function(){let e=$(this).data("option");$("#select_ordem").val(e).trigger("change")}))},initProductGallery:function(){let e=$(".product-gallery").hasClass("zoom-true"),t={slidesPerView:1,lazy:{loadPrevNext:!0},on:{init:function(){if(!e)return;1===this.slides.length&&(this.unsetGrabCursor(),this.allowTouchMove=!1);let t=$(".product-wrapper .product-gallery").find('.image[data-index="1"] .zoom');t.find("img:first").next().length||t.zoom({touch:!1,url:t.find("img").attr("src")})},slideChange:function(){if(!e)return;let t=this.activeIndex+1,a=$(".product-wrapper .product-gallery").find(`.image[data-index="${t}"] .zoom`);a.find("img:first").next().length||a.zoom({touch:!1,url:a.find("img").attr("src")})}}};$(".product-wrapper .product-gallery .product-thumbs .swiper-slide").length&&(this.settings.productThumbs=new Swiper(".product-wrapper .product-gallery .product-thumbs .thumbs-list",{slidesPerView:5,updateOnWindowResize:!0,centerInsufficientSlides:!1,watchSlidesProgress:!0,watchSlidesVisibility:!0,spaceBetween:10,direction:"vertical",navigation:{nextEl:".product-wrapper .product-gallery .product-thumbs .controls .next",prevEl:".product-wrapper .product-gallery .product-thumbs .controls .prev"},pagination:{el:".product-wrapper .product-gallery .product-thumbs .thumbs-list .dots",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},lazy:{loadPrevNext:!0},breakpoints:{0:{slidesPerView:5,centerInsufficientSlides:!0,direction:"horizontal"},575:{slidesPerView:5,centerInsufficientSlides:!0,direction:"horizontal"},768:{slidesPerView:5},1e3:{slidesPerView:5},1201:{slidesPerView:5}},on:{init:function(){$(".product-wrapper .product-gallery .product-thumbs").addClass("show")}}}),t.thumbs={autoScrollOffset:1,multipleActiveThumbs:!1,swiper:this.settings.productThumbs}),this.settings.productGallery=new Swiper(".product-wrapper .product-gallery .product-images",t)},recreateProductGallery:function(e){let t=$(".product-wrapper .product-form .product-name").text(),a="",o="";$.each(e,(function(e,i){let s=e+1;o+=`\n                    <div class="image swiper-slide ${1===s?"active":""}" data-index="${s}">\n                        <div class="zoom">\n                            <img class="swiper-lazy" data-src="${i.https}" alt="${t}">\n                        </div>\n                    </div>\n                `,a+=`<li class="swiper-slide ${1===s?"active":""}" data-index="${s}">\n                        <div class="thumb">\n                            <img src="${i.thumbs[90].https}" alt="${t}">\n                        </div>\n                    </li>\n                `})),theme.settings.productThumbs&&theme.settings.productThumbs.destroy(),theme.settings.productGallery&&theme.settings.productGallery.destroy(),$(".product-wrapper .product-gallery .product-images .image").remove(),$(".product-wrapper .product-gallery .product-thumbs .swiper-slide").remove(),$(".product-wrapper .product-gallery .product-images .swiper-wrapper").html(o),e.length>1?($(".product-wrapper .product-gallery .product-thumbs").addClass("show"),$(".product-wrapper .product-gallery .product-thumbs .thumbs-list .swiper-wrapper").html(a)):$(".product-wrapper .product-gallery .product-thumbs").removeClass("show"),theme.initProductGallery()},toggleProductVideo:function(){let e=this;$(".product-wrapper .product-box .product-video").on("click",(function(){$(".modal-video").find("iframe").attr("data-src",$(this).data("url")),$(".modal-video").addClass("show"),e.initLazyload(".iframe-lazy")})),$(".modal-video, .modal-video .close-icon").on("click",(function(e){$(e.target).hasClass("modal-info")||setTimeout((function(){$(".modal-video .video iframe").removeAttr("src").removeAttr("style").removeClass("loaded").removeAttr("data-was-processed data-ll-status")}),300)}))},goToProductReviews:function(){let e=this,t=0,a=0;$(".product-wrapper .product-box .product-form .product-rating .total").on("click",(function(){let o,i;a=jQuery("header.header").length>0?jQuery("header.header").height():0,t=jQuery(".message-top").length>0?jQuery(".message-top").height():0,$(window).width()<768?(o=".product-tabs div#coments",i=t+a+13):(o=".product-tabs div#coments",i=t+a+10),$(".product-tabs .tabs-content .tab-link-mobile.comments-link-tab:not(.active)").trigger("click"),e.scrollToElement(o,i)})),setTimeout((()=>{$("#form-comments .submit-review").on("click",(function(e){if(!$("#form-comments .stars .starn.star-on").length){$("#div_erro .blocoAlerta").text("AvaliaÃÂ§ÃÂ£o do produto obrigatÃÂ³ria, dÃÂª sua avaliaÃÂ§ÃÂ£o por favor").show(),setTimeout((()=>{$("#div_erro .blocoAlerta").hide()}),5e3)}}))}),3e3)},getShippingRates:function(){let e=this;var t=1;$(".shipping-form").on("submit",(function(a){a.preventDefault();let o=$("#form_comprar").find('input[type="hidden"][name="variacao"]'),i=$("#shippingSimulatorButton").data("url"),s=$("input",this).val().split("-");if(jQuery("#quant:visible").is(":visible")&&(t=jQuery("#quant:visible").val()),!o.length||""!==o.val())return o=o.val()||0,i=i.replace("cep1=%s",`cep1=${s[0]}`).replace("cep2=%s",`cep2=${s[1]}`).replace("acao=%s",`acao=${o}`).replace("dade=%s",`dade=${t}`),$(".product-shipping .result").removeClass("loaded").addClass("loading").html(e.getLoader("Carregando fretes...")),$.ajax({url:`https://viacep.com.br/ws/${s[0]+s[1]}/json/`,method:"get",dataType:"json",success:function(e){if(e.erro){let e="Cep inv&aacute;lido. Verifique e tente novamente.";$(".product-shipping .result").removeClass("loading").addClass("loaded").html(`<div class="error-message">${e}</div>`)}else $.ajax({url:i,method:"get",success:function(e){if(e.includes("N&atilde;o foi poss&iacute;vel estimar o valor do frete")){let e="N&atilde;o foi poss&iacute;vel obter os pre&ccedil;os e prazos de entrega. Tente novamente mais tarte.";return void $(".product-shipping .result").removeClass("loading").addClass("loaded").html(`<div class="error-message">${e}</div>`)}let t=$(e.replace(/Prazo de entrega: /gi,""));t.find("p .color").html().replace(/\s\s\\\s/g,"").replace(/ \\/g,",");t.find("table:first-child, p, table tr td:first-child").remove(),t.find("table, table th, table td").removeAttr("align class width border cellpadding cellspacing height colspan"),t.find("table").addClass("shipping-rates-table"),"Forma de Envio:"==t.find("table th:first-child").text()&&t.find("table th:first-child").html("Frete"),"Valor:"==t.find("table th:nth-child(2)").text()&&t.find("table th:nth-child(2)").html("Valor"),"Prazo de Entrega e ObservaÃÂ§ÃÂµes:"==t.find("table th:last-child").text()&&t.find("table th:last-child").html("Prazo"),t=t.children(),$(".product-shipping .result").removeClass("loading").addClass("loaded").html("").append(t)},error:function(e,t,a){console.error(`[Theme] Could not recover shipping rates. Error: ${a}`),""!==e.responseText&&console.error(`[Theme] Error Details: ${e.responseText}`);$(".product-shipping .result").removeClass("loading").addClass("loaded").html('<div class="error-message">N&atilde;o foi poss&iacute;vel obter os pre&ccedil;os e prazos de entrega. Tente novamente mais tarde.</div>')}})},error:function(e,t,a){console.error(`[Theme] Could not validate cep. Error: ${a}`),console.error(`[Theme] Error Details: ${e.responseJSON}`);$(".product-shipping .result").removeClass("loading").addClass("loaded").html('<div class="error-message">N&atilde;o foi poss&iacute;vel obter os pre&ccedil;os e prazos de entrega. Tente novamente mais tarde.</div>')}}),!1;$(".product-shipping .result").addClass("loaded").html('<div class="error-message">Por favor, selecione as varia&ccedil;&otilde;es antes de calcular o frete.</div>')}))},productBuyTogether:function(){let e=this;$(".compreJunto form .fotosCompreJunto").append('<div class="plus color to">=</div>'),$(".compreJunto .produto img").each((function(){let t=$(this).attr("src").replace("/90_","/180_"),a=$(this).parent().attr("href")||"",o=$(this).attr("alt");$(this).addClass("lazyload-buy-together").attr("src","").attr("data-src",t),e.initLazyload(".lazyload-buy-together"),""!==a?($(this).unwrap(),$(this).parents("span").after(`<a class="product-name" href="${a}">${o}</a>`)):$(this).parents("span").after(`<span class="product-name">${o}</span>`)}))},loadProductPaymentOptionsTab:function(){let e=$("#form_comprar").data("id"),t=$("#preco_atual").val(),a=$(".product-tabs .tabs-content .payment-tab");a.attr("data-loaded-price")!==t&&$.ajax({url:`/mvc/store/product/payment_options?loja=${theme.settings.storeId}&IdProd=${e}&preco=${t}`,method:"get",success:function(e){let o=$(e);o=o.find("#atualizaFormas").unwrap(),o=o.find("ul.Forma1").unwrap(),o.find("li").each((function(){let e=$("img",this).remove();$("a",this).prepend(e)})),o.find("table br").remove(),o.find("table td:first-child").remove(),o.find("table").removeAttr("id class width cellpadding cellspacing border style"),o.find("table td, table th").removeAttr("class width style"),o.find("li").removeAttr("id style"),o.find("li a").removeAttr("id class name"),o.find("li a img").removeAttr("border"),o.removeClass().addClass("payment-options"),o.find("li").addClass("option"),o.find("li a").attr("href","javascript:void(0)"),o.find("table").wrap('<div class="option-details"></div>'),o.find(".option-details").css("display","none"),a.attr("data-loaded-price",t),a.html("").append(o)}})},productTabsAction:function(){let e=this;$('.tab-link-mobile[href*="AbaPersonalizada"]').each((function(){let e=$(this).attr("href").split("#")[1];e=$(`#${e}`),$(e).detach().insertAfter(this)})),$(".product-tabs .tabs-content .tab[data-url]").each((function(){let t=$(this),a=t.data("url");t.hasClass("payment-tab")?e.loadProductPaymentOptionsTab():$.ajax({url:a,method:"get",success:function(e){t.html(e)}})})),$(".product-tabs .tabs-content .tab.payment-tab").on("click",".option a",(function(){let e=$(this).parent(),t=$(this).next();e.hasClass("show")?(e.removeClass("show"),t.slideUp()):(e.addClass("show"),t.slideDown())})),$(".product-tabs .tabs-nav .tab-link").on("click",(function(e){let t=$(this).closest(".product-tabs");if(!$(this).hasClass("active")){let e=$(this).attr("href").split("#")[1];e=$(`#${e}`),$(".tab-link",t).removeClass("active"),$(this).addClass("active"),$(".tabs-content .tab",t).fadeOut(),setTimeout((function(){e.fadeIn()}),300)}return e.preventDefault(),e.stopPropagation(),!1})),$(".product-tabs .tabs-content .tab-link-mobile").on("click",(function(e){let t=$(this).attr("href").split("#")[1];return t=$(`#${t}`),$(this).hasClass("active")?($(this).removeClass("active"),t.removeClass("active").slideUp()):($(".product-tabs .tabs-content .tab-link-mobile").removeClass("active"),$(".product-tabs .tabs-content .tab").removeClass("active").slideUp(),$(this).addClass("active"),t.addClass("active").slideDown()),e.preventDefault(),e.stopPropagation(),!1})),e.productTabActionsOnResize(),$(window).on("resize",(function(){e.productTabActionsOnResize()}))},productTabActionsOnResize:function(){if($(".product-tabs .tabs-nav li").length)if($(window).width()<768&&$(".product-tabs .tabs-nav .tab-link.active").length>0)$(".product-tabs .tabs-nav .tab-link").removeClass("active"),$(".product-tabs .tabs-content .tab-link-mobile:not(.description-link-tab)").removeClass("active"),$(".product-tabs .tabs-content .tab:not(.description-ab)").removeClass("active").slideUp();else if($(window).width()>=768&&0==$(".product-tabs .tabs-nav .tab-link.active").length){let e=$(".product-tabs .tabs-nav .tab-link").first(),t=e.attr("href").split("#")[1];$(".product-tabs .tabs-content .tab-link-mobile").removeClass("active"),e.addClass("active"),$(`#${t}`).show()}},productReviews:function(){let e=$(`<div class="product-comments">${window.commentsBlock}</div>`);e.find(".hreview-comentarios + .tray-hide").remove(),$.ajax({url:"/mvc/store/greeting",method:"get",dataType:"json",success:function(t){Array.isArray(t.data)?e.find("#comentario_cliente a.tray-hide").removeClass("tray-hide"):(e.find("#comentario_cliente form.tray-hide").removeClass("tray-hide"),e.find("#form-comments #nome_coment").val(t.data.name),e.find("#form-comments #email_coment").val(t.data.email),e.find('#form-comments [name="ProductComment[customer_id]"]').val(t.data.id)),$("#tray-comment-block").before(e),$("#form-comments #bt-submit-comments").before('<button type="submit" class="submit-review">Enviar</button>').remove(),$(".ranking .rating").each((function(){let e=Number($(this).attr("class").replace(/[^0-9]/g,""));for(i=1;i<=5;i++)i<=e?$(this).append('<div class="icon active"></div>'):$(this).append('<div class="icon"></div>')})),$("#tray-comment-block").remove(),theme.chooseProductRating(),theme.sendProductReview()}})},chooseProductRating:function(){$("#form-comments .rateBlock .starn").on("click",(function(){let e=$(this).data("message"),t=$(this).data("id");$(this).parent().find("#rate").html(e),$(this).closest("form").find("#nota_comentario").val(t),$(this).parent().find(".starn").removeClass("star-on"),$(this).prevAll().addClass("star-on"),$(this).addClass("star-on")}))},sendProductReview:function(){$("#form-comments").on("submit",(function(e){let t=$(this);$.ajax({url:t.attr("action"),method:"post",dataType:"json",data:t.serialize(),success:function(e){t.closest(".product-comments").find(".blocoAlerta").hide(),t.closest(".product-comments").find(".blocoSucesso").show(),setTimeout((function(){t.closest(".product-comments").find(".blocoSucesso").hide(),$("#form-comments #mensagem_coment").val(""),t.find("#nota_comentario").val(""),t.find("#rate").html(""),t.find(".starn").removeClass("star-on")}),8e3)},error:function(e){t.closest(".product-comments").find(".blocoSucesso").hide(),t.closest(".product-comments").find(".blocoAlerta").html(e.responseText).show()}}),e.preventDefault()}))},productRelatedCarousel:function(){$(".section-product-related .product").on("mouseenter",(function(){$(".showcase").addClass("z-index")})),$(".section-product-related product").on("mouseleave",(function(){$(".showcase").removeClass("z-index")})),new Swiper(".section-product-related .swiper-container",{slidesPerView:4,preloadImages:!1,loop:!1,lazy:{loadPrevNext:!0},navigation:{nextEl:".section-product-related .next",prevEl:".section-product-related .prev"},pagination:{el:".section-product-related .dots",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},breakpoints:{0:{slidesPerView:2},620:{slidesPerView:3},1200:{slidesPerView:4}}})},organizeProductHistory:function(){let e=$(".products-history .container").get(0);e&&(new MutationObserver((function(e,t){$.each(e,(function(){if("childList"==this.type&&"produtos"==$(this.target).prop("id"))return $('.products-history .container img[src*="sobconsulta"]').after('<div class="botao-commerce">Sob consulta</div>'),setTimeout((function(){$(".products-history .history-loader").removeClass("show")}),200),!1}))})).observe(e,{childList:!0,subtree:!0}),$(".products-history").on("click","#linksPag a",(function(){$(".products-history #produtos").html(""),$(".products-history .history-loader").addClass("show")})))},loadProductVariantImage:function(e){$.ajax({url:`/web_api/variants/${e}`,method:"get",success:function(e){let t=e.Variant.VariantImage;t.length&&theme.recreateProductGallery(t)},error:function(e,t,a){console.log(`[Theme] An error occurred while retrieving product variant image. Details: ${a}`)}})},detectProductVariantChanges:function(){let e=this;$(".product-variants").on("click",".lista_cor_variacao li[data-id]",(function(){e.loadProductVariantImage($(this).data("id"))})),$(".product-variants").on("click",".lista-radios-input",(function(){e.loadProductVariantImage($(this).find("input").val())})),$(".product-variants").on("change","select",(function(){e.loadProductVariantImage($(this).val())}))},organizeStoreReviewsPage:function(){$(".page-content .container .btns-paginator").length&&$(".page-content .container .btns-paginator").parent().addClass("store-review-paginator"),$(".page-content .container").append('<div class="botao-commerce show-modal-store-review" data-toggle="modal-theme" data-target=".modal-store-reviews">Deixe seu depoimento</div>'),$("#depoimento #aviso_depoimento").after('<button type="button" class="botao-commerce send-store-review">Enviar</button>'),$(".page-content h2:first").appendTo(".modal-store-reviews .modal-info"),$("#depoimento").appendTo(".modal-store-reviews .modal-info"),$("#comentario_cliente").remove(),$(".modal-store-reviews #depoimento a").remove(),$(".page-depoimentos .page-content").addClass("show"),$(".page-depoimentos").addClass("show-menu")},validateStoreReviewForm:function(){$(".modal-store-reviews #depoimento").validate({rules:{nome_depoimento:{required:!0},email_depoimento:{required:!0,email:!0},msg_depoimento:{required:!0},input_captcha:{required:!0}},messages:{nome_depoimento:{required:"Por favor, informe seu nome completo"},email_depoimento:{required:"Por favor, informe seu e-mail",email:"Por favor, preencha com um e-mail v&aacute;lido"},msg_depoimento:{required:"Por favor, escreva uma mensagem no seu depoimento"},input_captcha:{required:"Por favor, preencha com o c&oacute;digo da imagem de verifica&ccedil;&atilde;o"}},errorElement:"span",errorClass:"error-block",errorPlacement:function(e,t){"radio"===t.prop("type")?e.insertAfter(t.parent(".nota_dep")):t.is("textarea")?e.insertAfter(t.parent().find("h5")):e.insertAfter(t)}}),$(".modal-store-reviews #depoimento .send-store-review").on("click",(function(){let e=$(".modal-store-reviews #depoimento"),t=$(this);e.valid()&&(t.html("Enviando...").attr("disabled",!0),enviaDepoimentoLoja())}));let e=$("#aviso_depoimento").get(0);new MutationObserver((function(e,t){$(".depoimentos-modal #depoimento .send-store-review").html("Enviar").removeAttr("disabled")})).observe(e,{attributes:!0})},organizeNewsPage:function(){window.location.href.includes("busca_noticias")||$("#listagemCategorias").parent().before("<h1>Not&iacute;cias</h1>"),$(".noticias").find("li").wrapInner('<div class="box-noticia"></div>'),$(".page-busca_noticias .box-noticia").each((function(){let e=$(this).find("#noticia_imagem a").attr("href");$(this).find("p").after(`<a href="${e}" class="button-show">Ver mais</a>`)})),$(".page-busca_noticias .page-content").addClass("show"),$(".page-busca_noticias").addClass("show-menu")},organizeContactPage:function(){if($(".page-contact .page-content > .container").prepend('\n                <h1>Fale conosco</h1>\n                <p class="description">Precisa falar com a gente? Utilize uma das op&ccedil;&otilde;es abaixo para entrar em contato conosco.</p>\n                <div class="cols">\n                    <div class="box-form">                        \n                    </div>\n                    <div class="info-form"></div>\n                </div>\n            '),$($(".page-content .container3").eq(1)).appendTo(".info-form"),$($(".page-content .container3 .container2 .container2").eq(0)).appendTo(".box-form"),$(".info-form h3:contains(Empresa)").length&&$(".info-form h3:contains(Empresa)").parent().insertBefore($(".info-form h3:contains(Endere)").parent()),$(".info-form h3:contains(Endere)").parent().after($(".map-iframe")),$(".page-contact form img.image").after('<div class="flex justify-end"><span class="botao-commerce flex align-center justify-center">Enviar</span></div>').remove(),$(".page-contact #telefone_contato").removeAttr("onkeypress maxlength").addClass("phone-mask"),$(".page-contact .contato-telefones .block:nth-child(1)").length){let e=$(".page-contact .contato-telefones .block:nth-child(1)").text(),t=e.replace(/\D/g,"");$(".page-contact .contato-telefones .block:nth-child(1)").html(`<a href="tel:${t}" title="Ligue para n&oacute;s">${e}</a>`)}if($(".page-contact .contato-telefones .block:nth-child(2)").length){let e=$(".page-contact .contato-telefones .block:nth-child(2)").text(),t=e.replace(/\D/g,"");$(".page-contact .contato-telefones .block:nth-child(2)").html(`<a target="_blank" rel="noopener noreferrer" href="https://api.whatsapp.com/send?l=pt&phone=55${t}" title="Fale conosco no WhatsApp">${e}</a>`)}$(".page-contact .page-content").addClass("active")},gifts:function(){$('#form_presentes input[type="image"]').prev().html('<div class="botao-commerce">Continuar Comprando</div>'),$('#form_presentes input[type="image"]').wrap('<div class="relative-button"></div>').after('<button class="botao-commerce">Avan&ccedil;ar</button>').remove()},organizeNewsletterPage:function(){$(".page-newsletter .formulario-newsletter").length?($(".page-newsletter .formulario-newsletter .box-captcha input, .page-newsletter .formulario-newsletter .box-captcha-newsletter input").attr("placeholder","Digite o c&oacute;digo ao lado").trigger("focus"),$(".formulario-newsletter .newsletterBTimg").html("Enviar").removeClass().addClass("botao-commerce")):($(".page-newsletter .page-content").addClass("success-message-newsletter"),$(".page-newsletter .page-content.success-message-newsletter .board p:first-child a").addClass("botao-commerce").html("Voltar para p&aacute;gina inicial")),setTimeout((function(){$(".page-newsletter .page-content").addClass("show")}),200)},truckForm:function(){$(document).on("click",".header-wrapper__item.header-wrapper__item--truck .header-wrapper__form .header-wrapper__button",(function(e){let t=$(this).closest(".header-wrapper__form").attr("url-shipping"),a=$(this).parents(".header-wrapper__form").find("input").val();a.length>4?window.open(`${t}${a}`):alert("Preencha o campo corretamente")})),$('.header-wrapper__item.header-wrapper__item--truck .header-wrapper__form input[type="text"]').keyup((function(e){if(13==e.which){let e=$(this).closest(".header-wrapper__form").attr("url-shipping"),t=$(this).val();t.length>4?window.open(`${e}${t}`):alert("Preencha o campo corretamente")}}))},updateCartTotal:function(){}},$((function(){theme.resets(),theme.recoveryStoreId(),theme.scrollHeader(),theme.truckForm(),setTimeout((function(){theme.processRteElements(),theme.initLazyload(),theme.mainMenu(),theme.mainMenuMobile(),theme.initMasks(),theme.toggleModalTheme(),theme.overlay()}),20),setTimeout((function(){theme.bannerHome()}),40),$("html").hasClass("page-home")?(setTimeout((function(){theme.loadNews()}),40),theme.storeReviewsIndex()):$("html").hasClass("page-newsletter")?theme.organizeNewsletterPage():$("html").hasClass("page-catalog")||$("html").hasClass("page-search")?(theme.slideCatalog(),theme.sortMobile()):$("html").hasClass("page-product")?(theme.initProductGallery(),theme.toggleProductVideo(),theme.detectProductVariantChanges(),theme.goToProductReviews(),theme.getShippingRates(),theme.productBuyTogether(),theme.productTabsAction(),theme.productReviews(),theme.productRelatedCarousel(),theme.organizeProductHistory()):$("html").hasClass("page-busca_noticias")?(theme.organizeNewsPage(),theme.generateBreadcrumb("news-page-listing")):$("html").hasClass("page-noticia")?theme.generateBreadcrumb("news-page"):$("html").hasClass("page-depoimentos")?(theme.organizeStoreReviewsPage(),theme.validateStoreReviewForm()):$("html").hasClass("page-contact")?theme.organizeContactPage():$("html").hasClass("page-finalizar_presentes")&&theme.gifts()}));const customFeatures={customAddCart:function(){$.ajax({method:"GET",url:"/nocache/app.php?loja="+settings.store_id}).done((function(e,t,a){dataSession=JSON.parse(e),dataSession=void 0===dataSession.hash||null===dataSession.hash||""===dataSession.hash?dataLayer[0].visitorId:dataSession.hash,CarrinhoVue=new Vue({el:".js-vue-cart",data:{products:[],totalPrice:0,totalQuantity:0,loading:!1},methods:{getCartQuantity:function(){return new Promise(((e,t)=>{try{$.get(`/mvc/store/cart/count?loja=${settings.store_id}&hash=${Date.now()}`,(t=>{e(parseInt(t?.cart?.amount)||0)}))}catch(t){e(0)}}))},fetchCarrinho:async function(){this.loading=!0;var e=this;if(0==await this.getCartQuantity())return e.resetCart(),void(e.loading=!1);$.ajax({method:"GET",url:"/web_api/cart/"+dataSession}).done((function(t,a,o){e.totalPrice=0,e.totalQuantity=0,arrayProdutos=[],$(t).each((function(a,o){e.totalPrice=e.totalPrice+parseFloat(o.Cart.price)*o.Cart.quantity,e.totalQuantity=e.totalQuantity+parseInt(o.Cart.quantity),$.ajax({method:"GET",url:"/web_api/products/"+o.Cart.product_id}).done((function(i,s,r){0!=o.Cart.variant_id?$.ajax({method:"GET",url:"/web_api/variants/"+o.Cart.variant_id}).done((function(e,t,a){objetoFormatado={productDetails:i.Product,productImage:o.Cart.product_image,cart:o.Cart,cartPrice:parseFloat(o.Cart.price),productVariant:e.Variant,quantity:o.Cart.quantity},arrayProdutos.push(objetoFormatado)})):(objetoFormatado={productDetails:i.Product,productImage:o.Cart.product_image,cart:o.Cart,cartPrice:parseFloat(o.Cart.price),quantity:o.Cart.quantity},arrayProdutos.push(objetoFormatado)),a==$(t).length-1&&(e.products=arrayProdutos,e.loading=!1,$(".js-cart-price").text(e.totalPrice.toLocaleString("pt-BR",{minimumFractionDigits:2})),jQuery(".header .cart-toggle .cart-quantity").text(e.totalQuantity).addClass("has-items"))}))}))})).fail((function(t,a,o){404==JSON.parse(t.responseText).code&&e.resetCart(),e.loading=!1}))},deleteProduct:function(e,t,a,o){var i=this;$.ajax({type:"DELETE",url:"/web_api/carts/"+dataSession+"/"+e+"/"+t+"?additional_information="+o,contentType:"application/json; charset=utf-8"}).done((function(e,t,a){console.log("Removido com sucesso do carrinho!"),i.fetchCarrinho()})).fail((function(e,t,a){JSON.parse(e.responseText),toastr.error('Para remover produtos vinculados, clique em "Remover Tudo", ou clique em "FINALIZAR COMPRA" e remova na tela do carrinho.'),jQuery(".cart__footer .cart__options").addClass("active")}))},deleteAll:function(){var e=this;$.ajax({type:"DELETE",url:"/web_api/carts/"+dataSession,contentType:"application/json; charset=utf-8"}).done((function(t,a,o){console.log("Todos os produtos foram removidos com sucesso!"),e.fetchCarrinho(),$(".js-cart-price").text("0,00"),$(".header .cart-toggle .cart-quantity").text("0").removeClass("has-items")})).fail((function(e,t,a){JSON.parse(e.responseText)}))},resetCart:function(){this.products=[],this.totalPrice=0,$(".js-cart-price").text("0,00"),$(".header .cart-toggle .cart-quantity").text("0").removeClass("has-items"),$("body>.cart").removeClass("loading-cart")},closeDropdown:function(){$(".cart").addClass("cart-show")}},mounted:function(){this.fetchCarrinho()}}),$(document).on("tray:cart_preview:removed_item tray:cart_preview:removing_item tray:cart_preview_modal:removing_item continue_shopping",(function(e){CarrinhoVue.fetchCarrinho(),CarrinhoVue.closeDropdown(),$("body>.cart").removeClass("loading-cart")})),$(document).on("tray:cart_preview:added_item",(function(e){$("body>.cart").addClass("loading-cart"),CarrinhoVue.fetchCarrinho(),CarrinhoVue.closeDropdown()})),$(document).on("click",".cart .cart-backdrop, .cart .cart__vue__icon,.cart__footer .cart__bt.buy--continue",(function(){$(".cart").removeClass("cart-show"),$("body>.cart").removeClass("loading-cart")}))}))},customSliders:function(){function e(e,t,a,o){window.innerWidth>768?jQuery(t).length<=a&&jQuery(e).addClass("not-slide-drag"):jQuery(t).length<=o&&jQuery(e).addClass("not-slide-drag")}if(jQuery(".message-top .item").length>1){new Swiper(".message-top .swiper-container",{slidesPerView:1,effect:"slide",speed:1e3,lazy:{loadPrevNext:!0},navigation:{nextEl:".message-top .next",prevEl:".message-top .prev"},autoplay:{delay:3e3},loop:!0,breakpoints:{0:{slidesPerView:1},600:{slidesPerView:1},1e3:{slidesPerView:1}},on:{init:function(){jQuery(".message-top").addClass("active-slide")}}})}if(jQuery(".brands-custom").length>0){e(".brands-custom",".brands-custom .item.swiper-slide",6,2);new Swiper(".brands-custom .swiper-container",{effect:"slide",speed:1e3,spaceBetween:60,lazy:{loadPrevNext:!0},navigation:{nextEl:".brands-custom .next",prevEl:".brands-custom .prev"},autoplay:{delay:3e3},loop:!1,breakpoints:{0:{slidesPerView:2,spaceBetween:50},600:{slidesPerView:3,spaceBetween:50},1e3:{slidesPerView:4},1200:{slidesPerView:6}},pagination:{el:".brands-custom .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},on:{init:function(){jQuery(".brands-custom").addClass("active-slide")}}})}if(jQuery(".buy-sizes").length>0){e(".buy-sizes",".buy-sizes  .item.swiper-slide",6,2);new Swiper(".buy-sizes .swiper-container",{effect:"slide",speed:1e3,spaceBetween:30,lazy:{loadPrevNext:!0},navigation:{nextEl:".buy-sizes .next",prevEl:".buy-sizes .prev"},autoplay:{delay:3e3},loop:!1,breakpoints:{0:{slidesPerView:2},600:{slidesPerView:3},1e3:{slidesPerView:4},1200:{slidesPerView:6}},pagination:{el:".buy-sizes .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},on:{init:function(){jQuery(".buy-sizes").addClass("active-slide")}}})}const t=jQuery(".slider_parameters").attr("data-quantity-mobile"),a=jQuery(".slider_parameters").attr("data-quantity-desktop");$(".section-showcase .swiper-container").append('\n                <div class="prev">\n                    <i class="icon icon-arrow-left"></i>\n                </div>\n                <div class="next">\n                    <i class="icon icon-arrow-right"></i>\n                </div>            \n                <div class="dots"></div>\n            '),jQuery("main.site-main > .section-showcase .list-product__items").each((function(){window.innerWidth>768?jQuery(this).find(".swiper-slide").length<=a&&jQuery(this).parents(".section-showcase").addClass("not-slide-drag"):jQuery(this).find(".swiper-slide").length<=t&&jQuery(this).parents(".section-showcase").addClass("not-slide-drag")}));new Swiper(".section-showcase .swiper-container",{slidesPerView:3,effect:"slide",lazy:{loadPrevNext:!0},navigation:{nextEl:".section-showcase .next",prevEl:".section-showcase .prev"},loop:!1,autoplay:{delay:4500,disableOnInteraction:!1,pauseOnMouseEnter:!0},breakpoints:{0:{slidesPerView:t},600:{slidesPerView:3},1e3:{slidesPerView:a}},pagination:{el:".section-showcase .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},on:{init:function(){$(".section-showcase").addClass("show")}}});if(window.innerWidth<769&&jQuery(".banner-stripe .block-custom__content .item").length>2){jQuery(".banner-stripe .container").append('\n                        <div class="dots"></div>\n                    '),jQuery(".banner-stripe .container").addClass("swiper-container"),jQuery(".banner-stripe .block-custom__content").addClass("swiper-wrapper"),jQuery(".banner-stripe .block-custom__content .item").addClass("swiper-slide");new Swiper(".banner-stripe .swiper-container",{slidesPerView:2,effect:"slide",lazy:{loadPrevNext:!0},navigation:{nextEl:".banner-stripe .next",prevEl:".banner-stripe .prev"},autoplay:{delay:4500},loop:!1,pagination:{el:".banner-stripe .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},on:{init:function(){jQuery(".banner-stripe").addClass("active-slide")}}})}e(".custom-home-reviews",".custom-home-reviews .item.swiper-slide",3,1);new Swiper(".custom-home-reviews .swiper-container",{slidesPerView:3,effect:"slide",spaceBetween:20,lazy:{loadPrevNext:!0},navigation:{nextEl:".custom-home-reviews .next",prevEl:".custom-home-reviews .prev"},loop:!1,autoplay:{delay:4500,disableOnInteraction:!1,pauseOnMouseEnter:!0},breakpoints:{0:{slidesPerView:1},600:{slidesPerView:2},1e3:{slidesPerView:3}},pagination:{el:".custom-home-reviews .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},on:{init:function(){$(".custom-home-reviews").addClass("show")}}})},toggleTabsFooter:function(){window.innerWidth<769&&jQuery(".footer .cols .container>.box:not(.payments-seals):not(.box-logo) .title").click((function(){jQuery(this).next().slideToggle(),jQuery(this).toggleClass("active")}))},blogHome:function(){jQuery((function(){setTimeout((function(){document.getElementsByTagName("html")[0].getAttribute("data-store");var e="busca_noticias.php?loja="+document.getElementsByTagName("html")[0].getAttribute("data-store")+"&"+document.getElementsByTagName("html")[0].getAttribute("data-files"),t=jQuery(".noticias-content");jQuery.ajax({url:e,type:"GET",dataType:"html",contentType:"charset=iso-8859-1",success:function(e){var a=e.replace(/src/g,"src");t.html('<div class="noticias">'+jQuery(a).find(".noticias").html()+"</div>"),t.find("li").wrapInner('<div class="box-noticia"></div>').find("img").attr("width","400").attr("height","400"),t.find(".noticias li:nth-child(n+5)").remove(),!t.find(".noticias li").length&&jQuery(".section-notices").remove(),t.find(".noticias li img").addClass("loaded")}})}),400)}))},mobileSearch:function(){window.innerWidth<769&&(jQuery(".header .bg.header-mobile .search-mobile").click((function(){jQuery(".header .bg.header-mobile").toggleClass("active-search"),setTimeout((function(){jQuery(".header .header-search-wrapper .input-search").focus()}),200)})),jQuery(".header .bg.header-mobile .close-search-mobile").click((function(){jQuery(".header .bg.header-mobile").removeClass("active-search")})))},countdownProduct:function(){if(jQuery(".section-showcase.section-showcase--countdown").length>0){let e=jQuery(".section-showcase.section-showcase--countdown").attr("data-end-countdown").replace(/\s\s+/g," "),t=e.split(" ")[1],a=e.split(" ")[0].split("/")[0],o=e.split(" ")[0].split("/")[1],i=`${e.split(" ")[0].split("/")[2]}/${o}/${a} ${t}`;jQuery(".section-showcase.section-showcase--countdown .wrapper-header-countdown__timer").yuukCountDown({starttime:"2020/01/01 00:00:00",endtime:i,notStartCallBack:function(e){},startCallBack:function(e){},endCallBack:function(e){}})}},menuProductsCountdown:function(){jQuery(window).on("load",(function(){if(jQuery(".first-level.categoria-offer .categoria-offer__products .item").length>4&&jQuery(window).innerWidth()>767){let e=jQuery(".first-level.categoria-offer .categoria-offer__products").attr("data-countdown").replace(/\s\s+/g," "),t=e.split(" ")[1],a=e.split(" ")[0].split("/")[0],o=e.split(" ")[0].split("/")[1],i=`${e.split(" ")[0].split("/")[2]}/${o}/${a} ${t}`;jQuery(".first-level.categoria-offer .categoria-offer__products .list-product").addClass("swiper-container"),jQuery(".first-level.categoria-offer .categoria-offer__products .list-product__items").addClass("swiper-wrapper");new Swiper(".first-level.categoria-offer .categoria-offer__products .swiper-container",{slidesPerView:4,effect:"slide",lazy:{loadPrevNext:!0},navigation:{nextEl:".first-level.categoria-offer .categoria-offer__products .next",prevEl:".first-level.categoria-offer .categoria-offer__products .prev"},loop:!1,pagination:{el:".first-level.categoria-offer .categoria-offer__products .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},on:{init:function(){jQuery(".first-level.categoria-offer .categoria-offer__products").addClass("active-slide")}}});jQuery("li.first-level.categoria-offer .categoria-offer__countdown").length>0&&jQuery("li.first-level.categoria-offer .categoria-offer__countdown").yuukCountDown({starttime:"2020/01/01 00:00:00",endtime:i,notStartCallBack:function(e){},startCallBack:function(e){},endCallBack:function(e){}})}}))},fixedWhatsapp:function(){jQuery(window).on("load",(function(){jQuery("svg.whatsapp").length>0&&jQuery("svg.whatsapp").wrap('<div class="fixed-whatsapp"></div>')}))},stripeCountdownProducts:function(){if(jQuery(".countdown-products-params").length>0){let e=jQuery(".countdown-products-params").attr("value-products").split(","),t=`${jQuery(".countdown-products-params").attr("value-timer")}`.replace(/\s\s+/g," "),a=t.split(" ")[1],o=t.split(" ")[0].split("/")[0],i=t.split(" ")[0].split("/")[1],s=`${t.split(" ")[0].split("/")[2]}/${i}/${o} ${a}`;e.forEach((function(e){1==jQuery(".countdown-products-params").attr("value-active-product-showcase")&&(jQuery(`.product[product-ref="${e}"]`).find(".image").append('\n                            <div class="hidden list-product-countdown list-product-countdown--showcase">\n                                <div class="list-product-countdown__timer"></div>\n                            </div>\n                        '),jQuery(`.product[product-ref="${e}"] .list-product-countdown__timer`).yuukCountDown({starttime:"2020/01/01 00:00:00",endtime:s,notStartCallBack:function(e){},startCallBack:function(t){jQuery(`.product[product-ref="${e}"] .list-product-countdown`).removeClass("hidden")},endCallBack:function(e){}})),1==jQuery(".countdown-products-params").attr("value-active-product-page")&&(jQuery(`.product-wrapper .product-box .product-form[product-ref="${e}"]`).find("form#form_comprar").after('\n                            <div class="hidden list-product-countdown list-product-countdown--single">\n                                <div class="list-product-countdown__timer"></div>\n                            </div>\n                        '),jQuery(`.product-wrapper .product-box .product-form[product-ref="${e}"] .list-product-countdown__timer`).yuukCountDown({starttime:"2020/01/01 00:00:00",endtime:s,notStartCallBack:function(e){},startCallBack:function(t){jQuery(`.product-wrapper .product-box .product-form[product-ref="${e}"] .list-product-countdown`).removeClass("hidden")},endCallBack:function(e){}}))}))}},faqHome:function(){$(".custom-faq .custom-faq__quest").click((function(){$(this).hasClass("active")?($(this).removeClass("active"),$(this).next().slideUp()):($(".custom-faq .custom-faq__quest").removeClass("active"),$(".custom-faq .custom-faq__resp").slideUp(),$(this).toggleClass("active"),$(this).next().slideToggle())}))},quickBuy:function(){if($(".quick-buy").length>0){jQuery(".page-product .page-content .quick-buy").appendTo(".page-product .application");let e=0;e=window.innerWidth<769?1200:650,$("html").scrollTop()>e?$(".quick-buy").addClass("active"):$(".quick-buy").removeClass("active"),$(window).scroll((function(){$("html").scrollTop()>e?$(".quick-buy").addClass("active"):$(".quick-buy").removeClass("active")})),window.innerWidth>769?$(".quick-buy .quick-buy__content .quick-buy__action.quick-top button.to-top").click((function(){jQuery("html,body").animate({scrollTop:jQuery("main.site-main").offset().top-80},800)})):$(".quick-buy .quick-buy__content .quick-buy__action.quick-top button.to-top").click((function(){jQuery("html,body").animate({scrollTop:jQuery(".product-wrapper .product-box .product-form").offset().top-45},800)}))}},customValidationUserIsLogged:function(){$(document).on("ajaxComplete",((e,t,a)=>{a.url.includes("/mvc/store/greeting")&&(t.responseJSON.cartLoginToken?$("body").addClass("user-is-logged"):$("body").addClass("user-not-is-logged"))}))},adjustPositionMenuOnResize:function(){const e=()=>{$(window).on("resize",(()=>{$(".nav .list .second-level.products-show").each(((e,t)=>{(e=>{const{right:t}=$(e)[0].getBoundingClientRect(),a=$(window).width()-t-20+parseFloat($(e).attr("style")?.split("left: ")[1]?.split("px;")[0]||0);if(a>0)return $(e).css({left:"0"});$(e).css({left:a})})(t)}))}))};window.innerWidth<767||(e(),$(window).resize())},rulerProduct:function(){jQuery(".page-product").length&&(jQuery(".ruler-product").click((function(){jQuery(".modal-theme.product-ruler-modal").addClass("show")})),jQuery(".modal-theme.product-ruler-modal img").length>0&&jQuery(".ruler-product").addClass("active"),jQuery(".product-wrapper .product-tabs").length>0&&jQuery(".product-tabs #descricao").html(jQuery(".product-tabs #descricao").text()),window.innerWidth<769&&jQuery(".product-wrapper .product-form .product-tabs.short-tabs .tabs-content>.tab-link-mobile.description-link-tab").addClass("active"))},customLgpd:function(){"true"!=Cookies.get("site-lgpd")&&(jQuery(".site-lgpd").removeClass("loaded"),jQuery(".site-lgpd .site-lgpd__button").on("click",(function(){Cookies.set("site-lgpd","true",{expires:999}),jQuery(this).closest(".site-lgpd").addClass("loaded")})))},anchorFooter:function(){$(".action-footer-fixed__anchor").click((function(){$("html,body").animate({scrollTop:$("html").offset().top},1e3)})),$(window).scroll((function(){$("html").scrollTop()>400?$(".action-footer-fixed__anchor").addClass("active"):$(".action-footer-fixed__anchor").removeClass("active")}))},productsInfiniteCategory:function(){if(jQuery(".catalog-footer .pagination .page.last").length>0&&"1"==settings.show_infinite_products){jQuery(".catalog-cols .col-content").addClass("load-infinite-products");let urlPages=jQuery(".catalog-footer .pagination .page.last a").attr("href").split("pg=")[0],urlMaxPages=parseFloat(jQuery(".catalog-footer .pagination .page.last a").attr("href").split("pg=")[1]);jQuery.fn.isInViewport=function(){var e=jQuery(this).offset().top,t=e+jQuery(this).outerHeight(),a=jQuery(window).scrollTop(),o=a+jQuery(window).height();return t>a&&e<o};for(let e=2;e<=urlMaxPages;e++)jQuery(`<ul class="list-product flex f-wrap load-list" data-index="${e}"></ul>`).appendTo(".catalog-content .showcase-catalog");let intervalProducts=setInterval((function(){if(jQuery(".col-content.load-infinite-products .catalog-footer").isInViewport()){var currentIndex=jQuery(".catalog-content .showcase-catalog .list-product.load-list:not(.loaded-list):eq(0)").attr("data-index");$.get(`${urlPages}pg=${currentIndex}`,(function(data){var htmlContent=jQuery("<div>").html(data),specificContent=htmlContent.find(".catalog-content .showcase-catalog .list-product");jQuery(`.catalog-content .showcase-catalog .list-product.load-list[data-index="${currentIndex}"]:not(.loaded-list)`).append(specificContent.html()),jQuery(`.catalog-content .showcase-catalog .list-product.load-list[data-index="${currentIndex}"]:not(.loaded-list)`).find(".item").each((function(){jQuery(this).find(".space-image").find("img").each((function(){let e=jQuery(this).attr("data-src");jQuery(this).attr("src",`${e}`)}))})),specificContent.find("script").each((function(){let htmlFormated=jQuery(this).text().replace('document.addEventListener("DOMContentLoaded", function() {',"").replace("});","");eval(htmlFormated)})),jQuery(`.catalog-content .showcase-catalog .list-product.load-list[data-index="${currentIndex}"].loaded-list .item`).appendTo(".catalog-content .showcase-catalog .list-product:not(.load-list):eq(0)"),jQuery(`.catalog-content .showcase-catalog .list-product.load-list[data-index="${currentIndex}"]:not(.loaded-list)`).addClass("loaded-list"),jQuery(`.list-product.load-list.loaded-list[data-index="${urlMaxPages}"]`).length>0&&(clearInterval(intervalProducts),jQuery(".catalog-cols .col-content").addClass("load-products-success"))}))}}),300)}},freeShipping:function(){APP_FREESHIPPING={startShipping(){this.barProgressShipping()},barProgressShipping(){let e=Number(jQuery(".progress-bar-custom").attr("data-price")),t=jQuery(".progress-bar-custom").attr("text-primary"),a=jQuery(".progress-bar-custom").attr("text-success");var o="";let i=100-(e-(o=jQuery(".cart__total-price__value").text().length>9?jQuery(".cart__total-price__value").text().replace("R$","").replace(".","").replace(",","."):jQuery(".cart__total-price__value").text().replaceAll("R$","").replaceAll(",",".")))/e*100,s=jQuery(".progress-bar-custom .progress-bar-custom__descriptions"),r=jQuery(".progress-bar-custom .progress-bar-custom__percentage");s.html(`${t.replace("[valor]",`${(e-o).toLocaleString("pt-BR",{style:"currency",currency:"BRL"})}`)}`),o<e?(r.attr("style",`width:${i.toFixed(2)}%`),s.removeClass("sucess-bar")):(s.html(`${a}`),r.attr("style",`width:${i.toFixed(2)}%`),s.addClass("sucess-bar")),"0"==jQuery(".header .cart-toggle .cart-quantity").text()&&(r.attr("style","width:0%"),s.html(`${t.replace("[valor]",`${e}`)}`),s.removeClass("sucess-bar"))}},1==jQuery(".progress-bar-custom").attr("text-primary")?.includes("[valor]")?(jQuery(document).ajaxComplete((function(){jQuery(".progress-bar-custom").length>0&&setTimeout((function(){APP_FREESHIPPING.barProgressShipping()}),500)})),jQuery(document).ready((function(){jQuery(".progress-bar-custom").length>0&&APP_FREESHIPPING.startShipping()}))):jQuery(".progress-bar-custom").addClass("hide d-none hidden not-value")},adjustShowcaseCart:function(){jQuery(window).on("load",(function(){jQuery(".application>.custom-showcase-cart").length>0&&jQuery(".application>.custom-showcase-cart").insertBefore(".cart .dropdown__footer");new Swiper(".cart .custom-showcase-cart .swiper-container",{slidesPerView:3,effect:"slide",spaceBetween:35,lazy:{loadPrevNext:!0},navigation:{nextEl:".cart .custom-showcase-cart .next",prevEl:".cart .custom-showcase-cart .prev"},loop:!1,autoplay:!1,breakpoints:{0:{slidesPerView:1},600:{slidesPerView:1},1e3:{slidesPerView:1}},pagination:{el:".cart .custom-showcase-cart .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},on:{init:function(){$(".cart .custom-showcase-cart").addClass("show")}}})}))},addCarouselInstagramCustom:function(){if(0==jQuery(".template-instagram.not-slide-drag").length){window.innerWidth>768?jQuery(".template-instagram .item.swiper-slide").length<=4&&jQuery(".template-instagram").addClass("not-slide-drag"):jQuery(".template-instagram .item.swiper-slide").length<=2&&jQuery(".template-instagram").addClass("not-slide-drag");new Swiper(".template-instagram .swiper-container",{effect:"slide",speed:1e3,spaceBetween:20,lazy:{loadPrevNext:!0},navigation:{nextEl:".template-instagram .next",prevEl:".template-instagram .prev"},autoplay:{delay:3e3},loop:!1,breakpoints:{0:{slidesPerView:2},600:{slidesPerView:2},1e3:{slidesPerView:4},1200:{slidesPerView:4}},pagination:{el:".template-instagram .dots",type:"bullets",bulletClass:"dot",bulletActiveClass:"dot-active",clickable:!0},on:{init:function(){jQuery(".template-instagram").addClass("active-slide")}}})}},customStart:function(){this.customAddCart(),this.customSliders(),this.toggleTabsFooter(),this.blogHome(),this.mobileSearch(),this.countdownProduct(),this.menuProductsCountdown(),this.fixedWhatsapp(),this.stripeCountdownProducts(),this.faqHome(),this.quickBuy(),this.customValidationUserIsLogged(),this.rulerProduct(),this.customLgpd(),this.anchorFooter(),this.productsInfiniteCategory(),this.freeShipping(),this.adjustShowcaseCart(),this.addCarouselInstagramCustom(),window.innerWidth>767&&($(document).ready((()=>{this.adjustPositionMenuOnResize()})),$(window).on("load",(()=>{this.adjustPositionMenuOnResize()})),$(document).on("mouseenter",(()=>{this.adjustPositionMenuOnResize()}))),$(".page-maintenance").length>0&&$(".page-content form#formulario-newsletter a#btn_submit").trigger("click")}};customFeatures.customStart(),jQuery(document).ajaxComplete((function(e,t,a){a.url.includes("/web_api/")&&setTimeout((function(){jQuery(".modal.cart-preview .related-product-slide-content").length>0&&(jQuery(".cart .cart__footer .related-product-slide-content").remove(),jQuery(".modal.cart-preview .related-product-slide-content").appendTo(".cart .cart__footer"))}),200)}))})(jQuery);

/*Minify 21 05 25*/