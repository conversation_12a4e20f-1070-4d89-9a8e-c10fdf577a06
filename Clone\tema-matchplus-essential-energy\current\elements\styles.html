{% set extensionCss = minified ? '.min.css' : '.css' %}

{# Pre-connects #}
<link rel="preconnect" href="https://fonts.googleapis.com">

{# Styles Pre-loads #}
<link rel="preload" href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,400;0,600;0,700;1,400&display=swap" as="style" type="text/css" >
<link rel="preload" href="{{ asset('css/swiper.min.css')       }}" as="style" type="text/css" >
<link rel="preload" href="{{ asset('css/style' ~ extensionCss) }}" as="style" type="text/css" >

{# Theme Style #}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,400;0,600;0,700;1,400&display=swap" rel="stylesheet">
<link href="{{ asset('css/swiper.min.css')       }}" type="text/css" rel="stylesheet">
<link href="{{ asset('css/style' ~ extensionCss) }}" type="text/css" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('css/modal.css') }}" type="text/css">