/**
* Page Register
**/

.page-register,
.page-cadastro_bloqueado{
    
    .carrinho-tabs{
        display: none;
    }
    
    .page-content{
        
        > *{
            background: #fff;
            padding: 15px;
        }
         
        h1{
            border-bottom: 1px solid #eee;
            color: #3d4445;
            font: 400 26px/26px $font;
            margin: 0;
            padding: 0 0 15px;
            text-transform: none;
        }
        
        .Seguro,
        .carrinho-heading{
            display: none;
        }
    }
    
    #CadastroAbas{
        margin: 0 0 30px;
        
        @media screen and (max-width: $sm){
            margin-bottom: 15px;
        }
        
        &:after{
            clear: both;
            content: '';
            display: block;
        }
        
        a{
            background: #fff;
            border: 1px solid #eee;
            color: #3d4445;
            font: 400 15px/35px $font;
            padding: 0 30px;
            text-transform: none;
        }
        
        li{
            float: left;
        }
        
        .aberta{
            float: left;
            
            a{
                background: #9e9e9e;
                border: 1px solid #eee;
                color: #fff;
                padding: 0 30px;
            }
        }
    }
    
    #frm2{
        
        h2{
            color: #3d4445;
            font: 400 24px/24px $font;
            margin: 30px 0 10px;
            text-transform: none;
        }
        
        > .board{
            margin-top: 30px;
        }
        
        .text{
            font-size: 1.7rem;
            line-height: 1.8rem;
            margin: 3px 0 0;
            padding: 6px;
            vertical-align: middle;
            display: inline-block;
            
            &#cep_1,
            &#cobranca_cep_1,
            &#cep_2,
            &#cobranca_cep_2{
                margin-left: 10px;
            }
            
            &#numero_endereco,
            &#cobranca_numero_endereco{
                display: block;
            }
        }
        
        label{
            display: inline-block;
            margin-top: 15px;
            // vertical-align: top;
            @include typo(1.4rem, 400, $font, 2rem);
            
            &.dif{
                display: inline-block;
                margin-top: 0;
            }
        }
        
            .table{
                display: block;
            }
            
            .multienderecos.disabled p {
                display: none;
            }           
        
        .select {
            border: 1px solid #a9a9a9;
            font-size: 1.7rem;
            font-weight: 400;
            margin-top: 3px;
            padding: 6px;
        }
    }
    
    #endereco_cobranca_diferente{
        margin-top: 5px;
    }
    
    #cobranca_estado_principal {
        margin-right: 25px;
    }
    
    input#cep + label,
    input#habilita_ajax2 + label{
        display: inline-block;
        vertical-align: top;
    }
    
    input#cep + label + label,
    input#habilita_ajax2 + label + label{
        display: inline-block;
        margin-left: 20px;
        vertical-align: top;
        
        input{
            display: block;
        }
    }
    
    fieldset{
        color: #666;
        font-size: 0;
        line-height: 0;
        
        @media screen and (max-width: $sm){
            
            label{
                display: block;
                font-size: 0;
                line-height: 0;
            }
            
            br{
                display: none;
            }
        }
    }
    
    .botao-prosseguir-cadastro{
        background: #3d4445;
        border: none;
        color: #fff;
        height: 57px;
        padding: 18px 0;
        text-align: center;
        width: 200px;
        @include typo(2rem, 400, $font, 2rem);
    }
    
    #span_cep_nacional {
                
        .text {
            font: 400 14px/1px $font;
        }
    }
    
    #ou_estado_envio_nacional {
        display: inline-block;
        width: 100%;
    }
    
    @media screen and (min-width: 768px) and (max-width: 991px){
        #pf_nome_cliente,
        #razao_social,
        #pj_nome_cliente{
            width: 100%;
        }
        
        input#cep + label{
            width: 80%;
            
            input{
                width: 100%;
            }
        }
        
        #frm2{
            
            .text{
                
                &#cep_1{
                    margin-left: 0;
                }
            }
        }
    }
    
    @media screen and (max-width: $sm){
        
        .page-content{
            padding: 15px;
        }
        
        #pf_nome_cliente,
        #endereco_cliente,
        #cobranca_endereco{
            width: 100%;
        }
        
        #pf_data_nascimento{
            min-width: 120px;
        }
        
        #telefone_cliente{
            min-width: 200px;
        }
        
        #senha_cliente,
        #senha_cliente2{
            min-width: 180px;
        }
        
        #complemento,
        #cobranca_complemento{
            width: 170px;
        }
        
        .dif{
            font-size: 10px;
        }
        
        #email_cliente2_erro + br + label {
            font-size: 12px;
        }
        
        #cep_nacional_span,
        #span_cep_nacional{
            display: block;
        }
        
        .breadcrumb-spacer,
        .breadcrumb-item{
            display: none;    
        }
        
        .central-breadcrumb{
            padding: 0;
        }
        
        #frm2 {
        
            .text{
                display: block;
                font-size: 1.7rem;
                width: 100%;
                
                &#cep_1,
                &#cobranca_cep_1{
                    display: inline-block;
                    width: 56%;
                }
                
                &#cep_2,
                &#cobranca_cep_2{
                    display: inline-block;
                    margin-left: 5%;
                    width: 35%;
                }
            }
            
            .txt-dados-entrega,
            .txt-dados-cobranca,
            .txt-dados-pessoais,
            .txt-dados-loja{
                margin: 30px 0 0;
                
                & + br{
                    display: none;
                }
            }
            
            .dif{
                display: inline;
            }
            
            label{
                display: block;
            }
        }
        
        .botao-prosseguir-cadastro {
            display: block;
            float: none;
            margin: 15px 0 0 0;
            padding: 12px 50px;
        }
        
        #CadastroAbas{
            
            a{
                display: block;
                padding: 0;
                text-align: center;
            }
            
            .aberta{
                
                a{
                    padding: 0;
                }
            }
            
            li{
                float: left;
                width: 50%;
            }
            
            .mensagensErro{
                margin: 10px 0;
                width: 100%;
            }
        }
    }
}