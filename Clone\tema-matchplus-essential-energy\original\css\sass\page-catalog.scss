.page-catalog,
.page-search{
    
    .catalogo-comparator{
        background: #3d4445;
        color: #fff;
        margin: 0;
        padding: 10px;
        position: absolute;
        right: 0;
        text-align: left;
        text-transform: uppercase;
        top: -75px;
        width: auto;
        @include typo(1.2rem, 700, $font);
        
        @media screen and (max-width: $sm){
            clear: both;
            display: block;
            margin-top: 15px;
            position: static;
            text-align: center;
        }
        
        &:hover{
            background: #3d4445;
            color: #fff;
        }
    }
    
    .sidebar-hidden{
        @media screen and (max-device-width: 1024px){
            display: none !important;
        }
    }
    
    .smart-filter{
        background: #fff;
        border: 1px solid #e2e2e2;
        padding: 15px;
        margin-bottom: 30px;
        
        a,
        label{
            color: #3d4445;
            text-decoration: none;
            @include typo(1.2rem, 400, $font, 1.7rem);
            
            &:hover{
                text-decoration: underline;
            }
        }
        
        h3{
            color: #3d4445;
            margin-bottom: 10px;
            margin-top: 30px;
            text-transform: uppercase;
            @include typo(1.5rem, 400, $font);
        }
        
        li{
            margin-bottom: 5px;
        }
        
        .smartfilter-button {
            border: none;
            color: #000;
            font-size: 2rem;
            text-transform: uppercase;
            width: 100%;
            
            span,
            svg{
                display: inline-block;
                vertical-align: middle;
            }
            
            svg{
                margin-right: 10px;
            }
        }
    }    
    
    .catalog-menu.vertical-nav {
        border: none;
        height: auto;
        margin: -15px;
    }
    
    // checkbox
    .filter-checkbox{
        border: 1px solid #aaa;
        border-radius: 2px;
        display: inline-block;
        height: 14px;
        margin-right: 5px;
        position: relative;
        vertical-align: top;
        width: 14px;
        
        &.checked:before{
            border-bottom: 2px solid #000;
            border-left: 2px solid #000;
            content: '';
            height: 5px;
            left: 1px;
            position: absolute;
            top: 2px;
            width: 10px;
            @include prefix('transform', 'rotate(-45deg)');
        }
    }
    
    #open-filter:checked ~ .filters-list{
        display: block;
        margin-top: 30px;
    }
    
    .filters-list{
        @media screen and (max-width: 767px){
            display: none;
        }
        
        > div:first-child h3,
        > h3:first-child{
            margin-top: 0;
        }
        
        .applied-filters{
            a:hover{
                text-decoration: line-through;
            }
        }    
    }
    
    .filter-button{
        background: #9e9e9e;
        border: none;
        color: #fff;
        display: block;
        padding: 0;
        text-align: center;
        text-transform: uppercase;
        width: 100%;
        @include typo(1.5rem, 400, $font, 3.5rem);
    }
    
    .banner-title{
        margin-bottom: 15px;
    }
    
    .catalog-header{
        background: #fff;
        border: 1px solid #e2e2e2;
        padding: 15px;
        
        .catalogo-pagination{
            margin-top: 15px;
            position: relative;
        }
    }
    
    .catalog-content{
        margin-top: 15px;
    }
    
    .catalog-footer{
        background: #fff;
        border: 1px solid #e2e2e2;
        margin-top: 15px;
        padding: 15px;
        
        .catalogo-comparator{
            display: none;
        }
    }
    
    // catalog name
    .catalog-name{
        color: #3d4445;
        margin-bottom: 15px;
        @include typo(2.5rem, 700, $font);
    }
    
    // pagination
    .catalogo-display{
        color: #3d4445;
        font-size: 1.4rem;
        line-height: 2rem;
        
        @media screen and (max-width: $sm){
            float: none;
            text-align: center;
        }
    }
    
    .catalogo-pages,
    .pagination{
        color: #3d4445;
        font-size: 1.4rem;
        line-height: 22px;
        margin-top: 15px;
        
        @media screen and (max-width: $sm){
            float: none;
            text-align: center;
        }
    }
    
    .paginate-links{
        text-align: right;
        
        .page-current{
            border-color: #3d4445;
            color: #3d4445;            
        }
        
        @media screen and (max-width: $sm){
            float: none;
            margin-top: 15px;
            text-align: center;
        }
    }
    
    .btns-paginator,
    .page-link{
        border: 1px solid #aaa;
        color: #a2a2a2;
        display: inline-block;
        line-height: 2rem;
        margin: 0 5px;
        vertical-align: top;
        
        
        &.page-current{
            padding: 0 5px;    
        }
        
        &:hover{
            border-color: #000;
        }
        
        a{
            color: #aaa;
            display: block;
            line-height: 2rem;
            padding: 0 5px;
            
            &:hover{
                color: #000;
                text-decoration: none;
            }
        }
        
        &.selectedPg{
            border-color: #3d4445;
            color: #3d4445;
            padding: 0 5px;
        }
        
        @media screen and (max-width: $sm){
            &.btn-proxima-pagina,
            &.btn-ultima-pagina,
            &.btn-primeira-pagina,
            &.btn-pagina-anterior{
                display: block !important;
                margin: 15px 0 !important;
                text-align: center;
            }
        }
    }
    
    .separador-paginas{
        display: none;
    }
    
    // system filters
    .system-filter{
        
        .filtros{
            display: inline-block;
            float: none;
            margin-right: 15px;
            vertical-align: top;
            @include typo(1.4rem, 400, $font);
            
            @media screen and (min-width: 768px) and (max-width: 1199px){
                display: block;
                margin: 0 0 15px;
                width: 100%;
            }
            
            @media screen and (max-width: $sm){
                display: block;
                margin-top: 15px;
                margin-right: 0;
                
                .select{
                    display: block;
                    margin-top: 5px;
                }
            }
        
            .select{
                display: block;
                margin-top: 3px;
                padding: 8px;
                width: 150px;
                @include typo(1.4rem, 400, $font);
            }
        }
    }
    
    // breadcrumb
    .breadcrumb{
        margin-bottom: 15px;
        padding: 0;
    }
    
    .category-description{
        @include typo(1.4rem, 400, $font, 2rem);
        margin: 20px 0;
    }        
    
    // search advanced
    #vitrine-catalogo{
        
        #Vitrine + br,
        #Vitrine + br + .container,
        .container3:first-child{
            display: none;
        }
        
        #Vitrine{
            background: #fff;
            font-size: 1.4rem;
            padding: 30px;
            
            @media screen and (max-width: $sm){
                text-align: left;
            }
            
            input[type="text"]{
                padding: 5px;
                
                @media screen and (max-width: $sm){
                    border: 1px solid #e2e2e2;
                    display: block;
                    margin: 3px 0 15px;
                    width: 100%;
                    
                    &[name="palavra_busca"]{
                        margin-bottom: 3px;
                    }
                }
            }
            
            .select{
                padding: 5px;
                
                @media screen and (max-width: $sm){
                    border: 1px solid #e2e2e2;
                    display: block;
                    margin-top: 3px;
                    width: 100%;
                }
            }
            
            form p{
                line-height: 2rem;
                margin: 30px 0;
                
                input{
                    margin-right: 5px;
                }
            }
            
            input[type="image"]{
                background: url('../img/send-button.png') left top;
                height: 0;
                padding: 20px 65px;
                width: 0;
            }
        }
        
        .blocoAlerta{
            margin: 30px 0;
        }
        
        #div_erro{
            
            a h3{
                font-size: 1.6rem;
            }
        }
    }
    
    // comparator
    .button-compare{
        background: #000;
        color: #fff;
        float: right;
        margin-top: 18px;
        padding: 10px;
        text-decoration: none;
        text-transform: uppercase;
        @include typo(1.6rem, 400, $font, 1.6rem);
        
        @media screen and (max-width: $sm){
            display: block;
            float: none;
            margin: 15px 0;
            text-align: center;
        }
    }
    
    .compare-buttons{
        margin-top: 15px;
        
        a{
            color: #3d4445;
            text-decoration: none;
            @include typo(1.2rem, 400, $font, 1.7rem);
        }
    }
    
    // catalog empty
    .catalog-empty{
        background: #fff;
        padding: 0 30px 30px;
        text-align: center;
        
        p{
            font-size: 2rem;
        }
    }
    
    // visited
    .products-visited{
        background: #fff;
        border: 1px solid #e2e2e2;
        margin-top: 30px;
        padding: 30px;
        
        @media screen and (max-width: $sm){
            padding: 15px;
        }
        
        > h4{
            color: #9e9e9e;
            margin-bottom: 5px;
            text-transform: uppercase;
            @include typo(1.3rem, 400, $font, 1.3rem);
        }
        
        > h3{
            color: #3d4445;
            text-transform: uppercase;
            @include typo(2.5rem, 400, $font, 2.5rem);
        }
        
        .visitados{
            position: relative;
        }
        
        .visitados_produtos{
            overflow: hidden;
            
            @media screen and (max-width: $sm){
                padding-bottom: 85px;
            }
        }
        
        .paginacao_ajax{
            position: absolute;
            bottom: 0;
            left: 0;
            
            @media screen and (max-width: $sm){
                border-top: 1px solid #e8e8e8;
                padding-top: 15px;
                width: 100%;
            }
        }
        
        .clearVisiteds{
            background: #9e9e9e;
            bottom: 0;
            color: #fff;
            padding: 0 10px;
            position: absolute;
            text-decoration: none;
            right: 300px;
            @include typo(1.5rem, 400, $font, 2.5rem);
            
            @media screen and (max-width: $sm){
                position: static;
                width: 100%;
                right: 0;
            }
            
            @media screen and (max-width: 1200px){
                right: 0;
            }
        }
        
        .myNavigation{
            background: #9e9e9e;
            color: #fff;
            display: block;
            text-align: center;
            text-decoration: none;
            @include typo(1.5rem, 400, $font, 2.5rem);
        }
        
        .total_produtos{
            color: #aaa;
            margin-right: 30px;
            @include typo(1.5rem, 400, $font, 2.5rem);
            
            @media screen and (max-width: $sm){
                display: block;
                margin: 0;
            }
        }
        
        .paginacao_ajax_prod{
            color: #aaa;
            @include typo(1.5rem, 400, $font, 2.5rem);
            
            #linksPag{
                
                a{
                    border-radius: 25px;
                    color: #aaa;
                    display: inline-block;
                    height: 25px;
                    text-align: center;
                    text-decoration: none;
                    width: 25px;
                    @include typo(1.5rem, 400, $font, 2.5rem);
                }    
                
                .pageON{
                    background: #9e9e9e;
                    color: #fff !important;
                }
            }
        }
        
        .visitados_itens{
            border-left: 1px solid #eee;
            float: right;
            min-height: 360px;
            // min-width: 280px;
            padding-left: 15px;
            line-height: 2;
                
            @media screen and (max-width: 1199px){
                border: none;
                float: none;
                min-width: 100%;
                margin-top: 15px;
                padding: 15px 0 0;
            }
            
            h4{
                color: #aaa;
                margin: 0 0 10px;
                @include typo(1.5rem, 400, $font, 1.8rem);
            }
            
            .itens{
                border-top: 1px solid #eee;
                height: 165px;
                margin-bottom: 15px;
                padding-bottom: 15px;
                
                &:first-child{
                    border-top: none;
                }
                
                &:nth-child(2){
                    margin-bottom: 25px;
                    padding-bottom: 0;
                    padding-top: 15px;
                }
                
                a{
                    color: #aaa;
                }
            }
        }
        
        #produtos{
            
            @media screen and (max-width: 1199px){
                margin-bottom: 100px;
                padding-bottom: 30px;
                border-bottom: 1px solid #eee;
            }
            
            > ul{
                float: left;
                padding: 0 5px;
                width: 33%;
                
                @media screen and (max-width: 1199px){
                    float: none;
                    padding: 0;
                    width: 100%;
                }
            }
        }
        
        .FotoLista{
            border-bottom: 1px solid #eee;
            font-size: 0;
            height: 180px;
            margin-bottom: 15px;
            text-align: center;
            
            &:before{
                content: '';
                display: inline-block;
                height: 180px;
                vertical-align: middle;
                width: 1%;
            }
            
            img{
                display: inline-block;
                max-width: 99%;
                vertical-align: middle;
            }
        }
        
        .NomeProdLista{
            color: #3d4445;
            display: block;
            height: 36px;
            margin-bottom: 15px;
            overflow: hidden;
            text-transform: none;
            @include typo(1.5rem, 400, $font, 1.8rem);
            
            @media screen and (max-width: 1199px){
                margin-bottom: 7px;
                @include typo(1.5rem, 400, $font, 1.3rem);
            }
        }
        
        .ValoresLista{
            color: #000;
            @include typo(1.2rem, 400, $font, 1.4rem);
            
            .oculta_destaque{
                display: none;
            }
            
            .precoAvista{
                color: #000;
                display: block;
                height: 18px;
                overflow: hidden;
                @include typo(1.5rem, 700, $font, 1.8rem);
                
                & + br{
                    display: none;
                }
            }
            
            span,
            strong{
                color: #000;
                @include typo(1.2rem, 400, $font, 1.4rem);
                
                @media screen and (max-width: 1199px){
                    @include typo(1rem, 400, $font, 1rem);
                }
            }
        }
        
        .precode{
            color: #aaa;
        }
    }    
}