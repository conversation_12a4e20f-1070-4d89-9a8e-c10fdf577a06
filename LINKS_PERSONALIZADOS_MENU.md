# Links Personalizados no Menu - Tema MatchPlus

## Descrição
Esta funcionalidade permite adicionar links personalizados ao menu superior do tema MatchPlus da Tray, junto às categorias existentes.

## Como Configurar

### 1. Acesso às Configurações
1. Acesse o painel administrativo da sua loja Tray
2. Vá em **Configurações** > **Personalizar Tema**
3. Procure pela seção **"Links Personalizados do Menu"**

### 2. Configuração dos Links
Você pode configurar até 3 links personalizados. Para cada link, você pode definir:

- **Nome do Link**: O texto que aparecerá no menu (ex: "Sintomas", "Sobre Nós", "Contato")
- **URL do Link**: O endereço para onde o link deve levar (ex: "/sintomas", "/sobre-nos", "https://exemplo.com")
- **Abrir em nova aba**: Marque esta opção se quiser que o link abra em uma nova aba do navegador

### 3. Exemplo de Configuração
Para adicionar o link "Sintomas" que você mencionou:

1. **Nome do Link 1**: `Sintomas`
2. **URL do Link 1**: `/sintomas`
3. **Abrir em nova aba**: Deixe desmarcado (a menos que queira abrir em nova aba)

### 4. Tipos de URLs Suportadas
- **URLs internas**: `/sintomas`, `/sobre-nos`, `/contato`
- **URLs externas**: `https://exemplo.com`, `https://blog.minhaloja.com`
- **URLs de categorias**: `/categoria-especial`
- **URLs de páginas**: `/politica-de-privacidade`

## Onde os Links Aparecem

### Menu Desktop
Os links personalizados aparecerão no menu superior, após as categorias e antes do menu de ofertas (se ativo).

### Menu Mobile
Os links também aparecerão no menu mobile, na mesma posição relativa.

## Estilização
Os links personalizados herdam o estilo das configurações de cores do tema:
- **Cor normal**: Usa a cor configurada em "Cor do texto do menu"
- **Cor ao passar o mouse**: Usa a cor configurada em "Cor de destaque do cabeçalho"

## Exemplo Prático
Para sua loja Essential Energy, você pode configurar:

1. **Link 1**:
   - Nome: `Sintomas`
   - URL: `https://essentialenergy.commercesuite.com.br/sintomas`
   - Nova aba: Não

2. **Link 2**:
   - Nome: `Sobre Nós`
   - URL: `/sobre-nos`
   - Nova aba: Não

3. **Link 3**:
   - Nome: `Blog`
   - URL: `https://blog.essentialenergy.com.br`
   - Nova aba: Sim

## Observações Importantes
- Os links aparecerão na ordem: Link 1, Link 2, Link 3
- Se você deixar o nome ou URL vazio, o link não aparecerá
- URLs externas devem incluir `http://` ou `https://`
- URLs internas podem começar com `/` ou ser apenas o slug da página

## Suporte
Se você tiver dúvidas sobre a implementação ou precisar de ajuda adicional, consulte o manual do tema MatchPlus ou entre em contato com o suporte da Samá Themes.
