# 🧪 Teste da Página de Contato - Versão Melhorada

## 🎯 **Problema Identificado**
As modificações anteriores não estavam funcionando porque os elementos são carregados dinamicamente pela plataforma Tray.

## 🔧 **Nova Abordagem Implementada**

### **1. JavaScript Mais Robusto**
- **Múltiplas tentativas** de remoção (100ms, 500ms, 1000ms)
- **Seletores mais específicos** para encontrar elementos
- **Regex para CNPJ** mais precisa
- **Remoção inteligente** de seções completas

### **2. CSS de Backup**
- **Regras CSS** para ocultar elementos como fallback
- **!important** para sobrescrever estilos da plataforma
- **Múltiplos seletores** para cobrir diferentes estruturas

### **3. Abordagem Híbrida**
- **JavaScript** para remoção dinâmica
- **CSS** para ocultação garantida
- **Múltiplos timeouts** para diferentes momentos de carregamento

## 📋 **Modificações Implementadas**

### **JavaScript (main.js):**
```javascript
function removeUnwantedElements() {
    // Remove campo "Empresa" do formulário
    $('#empresa, input[name="empresa"], input[id*="empresa"]').each(function() {
        $(this).closest('tr, div, p').remove();
    });
    
    // Remove CNPJ com regex
    $('.page-contact *').each(function() {
        var text = $(this).text();
        if (text.match(/cnpj|c\.n\.p\.j/i)) {
            // Remove ou limpa o texto
        }
    });
    
    // Remove seção "Nosso Endereço"
    $('.page-contact h3, .page-contact h2').each(function() {
        if ($(this).text().toLowerCase().includes('endere')) {
            $(this).parent().remove();
        }
    });
}

// Executa em múltiplos momentos
setTimeout(removeUnwantedElements, 100);
setTimeout(removeUnwantedElements, 500);
setTimeout(removeUnwantedElements, 1000);
```

### **CSS (custom.css.html):**
```css
/* Ocultar campo empresa */
.page-contact #empresa,
.page-contact input[name="empresa"] {
    display: none !important;
    visibility: hidden !important;
}

/* Ocultar elementos com CNPJ */
.page-contact .info-form *:contains("CNPJ") {
    display: none !important;
}

/* Ocultar seção de endereço */
.page-contact .info-form h3:contains("Endereço") {
    display: none !important;
}
```

## 🧪 **Como Testar**

### **1. Limpar Cache**
```bash
# Limpe o cache do navegador
Ctrl + F5 (Windows)
Cmd + Shift + R (Mac)
```

### **2. Verificar Elementos**
1. Acesse: `https://essentialenergy.commercesuite.com.br/contato`
2. Abra o **DevTools** (F12)
3. Verifique se os elementos foram removidos:
   - Campo "Empresa" no formulário
   - CNPJ nos dados da empresa
   - Seção "Nosso Endereço"

### **3. Console do Navegador**
Execute no console para testar manualmente:
```javascript
// Verificar se campo empresa existe
console.log('Campo empresa:', $('#empresa').length);

// Verificar elementos com CNPJ
console.log('Elementos com CNPJ:', $('*:contains("CNPJ")').length);

// Verificar seção endereço
console.log('Seção endereço:', $('*:contains("Endereço")').length);
```

## 🔍 **Diagnóstico de Problemas**

### **Se ainda não funcionar:**

1. **Verificar se o JavaScript está carregando:**
   ```javascript
   // No console do navegador
   console.log('Função organizeContactPage existe:', typeof organizeContactPage);
   ```

2. **Verificar estrutura HTML:**
   ```javascript
   // Ver estrutura da página
   console.log($('.page-contact').html());
   ```

3. **Executar remoção manual:**
   ```javascript
   // Executar função manualmente
   setTimeout(function() {
       $('#empresa').closest('tr').remove();
       $('*:contains("CNPJ")').remove();
       $('*:contains("Endereço")').parent().remove();
   }, 2000);
   ```

## 🛠️ **Soluções Alternativas**

### **Opção 1: CSS Mais Agressivo**
Se o JavaScript não funcionar, podemos usar CSS mais específico:
```css
/* Ocultar por conteúdo de texto */
.page-contact [class*="empresa"],
.page-contact [id*="empresa"],
.page-contact tr:has(input[name="empresa"]) {
    display: none !important;
}
```

### **Opção 2: Modificar HTML Diretamente**
Se a plataforma permitir, modificar o template HTML da página de contato.

### **Opção 3: Script Externo**
Criar um script separado que execute após o carregamento completo:
```javascript
$(window).on('load', function() {
    setTimeout(function() {
        // Remoções aqui
    }, 2000);
});
```

## 📞 **Próximos Passos**

1. **Teste a página** após aplicar as modificações
2. **Verifique no navegador** se os elementos foram removidos
3. **Se não funcionar**, me informe qual elemento ainda aparece
4. **Podemos ajustar** a abordagem conforme necessário

## 🔄 **Versões de Fallback**

Se nada funcionar, temos estas opções:
- **CSS puro** para ocultar elementos
- **JavaScript mais agressivo** com intervalos
- **Modificação do template** HTML (se possível)
- **Script personalizado** executado após carregamento

---

**Teste agora e me informe o resultado!** 🚀
