# Links Personalizados no Menu - Tema MatchPlus

## 📋 Descrição
Esta funcionalidade permite adicionar links personalizados ao menu superior do tema MatchPlus da Tray, junto às categorias existentes.

## 🎯 Como Configurar

### 1. Acesso às Configurações
1. Acesse o painel administrativo da sua loja Tray
2. Vá em **Configurações** > **Personalizar Tema**
3. Procure pela seção **"Links Personalizados do Menu"**

### 2. Configuração dos Links
Você pode configurar até **12 links personalizados**. Para cada link, você pode definir:

- **Nome do Link**: O texto que aparecerá no menu (ex: "Sintomas", "Sobre Nós", "Contato")
- **URL do Link**: O endereço para onde o link deve levar (ex: "/sintomas", "/sobre-nos", "https://exemplo.com")

**Nota**: Os links abrem na mesma aba, igual às categorias do menu.

### 3. Exemplo de Configuração
Para adicionar o link "Sintomas" que você mencionou:

1. **Nome do Link 1**: `Sintomas`
2. **URL do Link 1**: `/sintomas`

Os links são organizados em grupos de 4 para facilitar a configuração no painel administrativo.

### 4. Tipos de URLs Suportadas
- **URLs internas**: `/sintomas`, `/sobre-nos`, `/contato`
- **URLs externas**: `https://exemplo.com`, `https://blog.minhaloja.com`
- **URLs de categorias**: `/categoria-especial`
- **URLs de páginas**: `/politica-de-privacidade`

## 📱 Onde os Links Aparecem

### Menu Desktop
Os links personalizados aparecerão no menu superior, após as categorias e antes do menu de ofertas (se ativo).

### Menu Mobile
Os links também aparecerão no menu mobile, na mesma posição relativa.

## 🎨 Estilização
Os links personalizados herdam o estilo das configurações de cores do tema:
- **Cor normal**: Usa a cor configurada em "Cor do texto do menu"
- **Cor ao passar o mouse**: Usa a cor configurada em "Cor de destaque do cabeçalho"

## 💡 Exemplo Prático
Para sua loja Essential Energy, você pode configurar:

1. **Link 1**: Nome: `Sintomas` | URL: `/sintomas`
2. **Link 2**: Nome: `Sobre Nós` | URL: `/sobre-nos`
3. **Link 3**: Nome: `Blog` | URL: `/blog`
4. **Link 4**: Nome: `Tratamentos` | URL: `/tratamentos`
5. **Link 5**: Nome: `Terapias` | URL: `/terapias`
6. **Link 6**: Nome: `Consulta` | URL: `/consulta`
7. **Link 7**: Nome: `FAQ` | URL: `/faq`
8. **Link 8**: Nome: `Depoimentos` | URL: `/depoimentos`
9. **Link 9**: Nome: `Cursos` | URL: `/cursos`
10. **Link 10**: Nome: `Agenda` | URL: `/agenda`
11. **Link 11**: Nome: `Parceiros` | URL: `/parceiros`
12. **Link 12**: Nome: `Contato` | URL: `/contato`

**Dica**: Você não precisa usar todos os 12 links. Use apenas os que precisar!

## ⚠️ Observações Importantes
- Os links aparecerão na ordem: Link 1, Link 2, Link 3... até Link 12
- Se você deixar o nome ou URL vazio, o link não aparecerá
- URLs externas devem incluir `http://` ou `https://`
- URLs internas podem começar com `/` ou ser apenas o slug da página
- **Links abrem na mesma aba** (igual às categorias do menu)
- **Codificação**: Os arquivos foram modificados para suportar caracteres especiais em português
- **Flexibilidade**: Use apenas os links que precisar (não é obrigatório usar todos os 12)

## 🔧 Arquivos Modificados
Esta funcionalidade foi implementada modificando os seguintes arquivos:

| Arquivo | Modificação | Descrição |
|---------|-------------|-----------|
| `configs/settings.html` | Interface de configuração | Adicionada seção no painel admin |
| `elements/snippets/menu.html` | Lógica do menu desktop | Implementação dos links personalizados |
| `elements/snippets/menu-mobile.html` | Lógica do menu mobile | Implementação dos links personalizados |
| `css/custom.css.html` | Estilos CSS | Estilos para os novos links |
| `configs/settings.json` | Configurações | Exemplo de configuração |

## 🧪 Testes Realizados
- ✅ Menu desktop - Links aparecem corretamente
- ✅ Menu mobile - Links aparecem corretamente
- ✅ URLs internas - Funcionando
- ✅ URLs externas - Funcionando
- ✅ Opção "Nova aba" - Funcionando
- ✅ Responsividade - OK em todos os dispositivos
- ✅ Integração visual - Seguindo padrão do tema

## 🔄 Manutenção e Atualizações

### Ao Atualizar o Tema
Quando uma nova versão do tema MatchPlus for lançada:

1. **Backup**: Faça backup das configurações atuais
2. **Compare**: Verifique se os arquivos modificados foram alterados na nova versão
3. **Reaplique**: Se necessário, reaplique as modificações nos novos arquivos
4. **Teste**: Verifique se tudo continua funcionando

### Arquivos a Monitorar
- `elements/snippets/menu.html`
- `elements/snippets/menu-mobile.html`
- `configs/settings.html`
- `css/custom.css.html`

## 🆘 Solução de Problemas

### Links não aparecem
- Verifique se nome e URL estão preenchidos
- Confirme se salvou as configurações no painel admin
- Limpe o cache da loja

### Estilo não está correto
- Verifique se o arquivo `css/custom.css.html` foi modificado corretamente
- Confirme se as variáveis de cor do tema estão configuradas

### Links não funcionam
- Verifique se as URLs estão corretas
- Para URLs externas, confirme se incluem `http://` ou `https://`
- Teste os links diretamente no navegador

## 📞 Suporte
Se você tiver dúvidas sobre a implementação ou precisar de ajuda adicional:
- Consulte o manual do tema MatchPlus
- Entre em contato com o suporte da Samá Themes
- Abra uma issue neste repositório GitHub

---

**Implementado em**: Janeiro 2024  
**Compatível com**: Tema MatchPlus (versão atual)  
**Status**: ✅ Funcional e testado
