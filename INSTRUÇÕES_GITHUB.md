# 📋 Instruções para Organizar o Repositório GitHub

## 🎯 Estrutura Final Recomendada

Organize seu repositório com a seguinte estrutura:

```
tema-matchplus-essential-energy/
├── README.md                    # ✅ Já criado
├── CHANGELOG.md                 # ✅ Já criado  
├── INSTRUÇÕES_GITHUB.md         # ✅ Este arquivo
├── original/                    # 📁 Você precisa criar
│   └── [arquivos do tema original]
├── current/                     # 📁 Você precisa criar
│   └── [arquivos do tema modificado]
└── docs/                        # 📁 Você precisa criar
    └── LINKS_PERSONALIZADOS_MENU.md  # ✅ J<PERSON> criado
```

## 📂 Passos para Organizar

### 1️⃣ **Criar as Pastas**
No seu repositório GitHub, crie as seguintes pastas:
- `original/`
- `current/`
- `docs/`

### 2️⃣ **Pasta `original/`**
Extraia o arquivo ZIP original do tema e coloque todos os arquivos aqui:
```
original/
├── configs/
├── css/
├── elements/
├── img/
├── js/
├── layouts/
└── pages/
```

### 3️⃣ **Pasta `current/`**
Copie todos os arquivos da pasta `original/` para `current/` e depois aplique as modificações que fizemos:

**Arquivos que você precisa modificar em `current/`:**

#### `current/configs/settings.html`
- Adicionar a seção "Links Personalizados do Menu" (linhas 8053-8122)

#### `current/elements/snippets/menu.html`
- Adicionar o código dos links personalizados (após linha 143)

#### `current/elements/snippets/menu-mobile.html`
- Adicionar o código dos links personalizados mobile (após linha 69)

#### `current/css/custom.css.html`
- Adicionar os estilos CSS (após linha 242)

#### `current/configs/settings.json`
- Adicionar as configurações de exemplo

### 4️⃣ **Pasta `docs/`**
Mova o arquivo `LINKS_PERSONALIZADOS_MENU.md` para dentro desta pasta.

## 💻 Comandos Git Sugeridos

### **Primeiro Commit - Tema Original**
```bash
git add original/
git add README.md CHANGELOG.md
git commit -m "feat: adiciona tema MatchPlus original e documentação inicial

- Tema MatchPlus versão base (backup)
- README com instruções de uso
- CHANGELOG para controle de versões
- Estrutura inicial do repositório"
```

### **Segundo Commit - Modificações**
```bash
git add current/
git add docs/
git commit -m "feat: implementa links personalizados no menu

✨ Funcionalidades:
- Interface de configuração no painel admin
- Suporte a 3 links personalizados configuráveis
- Funciona em menu desktop e mobile
- Opção para abrir em nova aba
- Estilos integrados com o tema

📝 Arquivos modificados:
- configs/settings.html - Interface de configuração
- elements/snippets/menu.html - Menu desktop
- elements/snippets/menu-mobile.html - Menu mobile  
- css/custom.css.html - Estilos CSS
- configs/settings.json - Configurações exemplo

📖 Documentação:
- docs/LINKS_PERSONALIZADOS_MENU.md - Guia completo"
```

### **Terceiro Commit - Limpeza**
```bash
git rm INSTRUÇÕES_GITHUB.md
git commit -m "docs: remove arquivo de instruções temporário"
```

## 🏷️ **Criar Tag da Versão**
```bash
git tag -a v1.1.0 -m "Versão 1.1.0 - Links personalizados no menu"
git push origin v1.1.0
```

## ✅ **Checklist Final**

- [ ] Pasta `original/` com tema original completo
- [ ] Pasta `current/` com tema modificado
- [ ] Pasta `docs/` com documentação
- [ ] `README.md` na raiz
- [ ] `CHANGELOG.md` na raiz
- [ ] Commits organizados com mensagens descritivas
- [ ] Tag da versão criada
- [ ] Repositório privado (recomendado para temas comerciais)

## 🔄 **Para Futuras Atualizações**

Quando uma nova versão do tema for lançada:

1. **Backup da versão atual**:
   ```bash
   git checkout -b backup-v1.1.0
   git checkout main
   ```

2. **Atualizar original**:
   - Substituir conteúdo de `original/` com nova versão
   - Commit: `git commit -m "update: tema original para versão X.X.X"`

3. **Comparar e aplicar modificações**:
   ```bash
   # Ver diferenças
   git diff original/ current/
   
   # Aplicar modificações na nova versão
   # Testar tudo
   # Commit das modificações preservadas
   ```

## 📞 **Dúvidas?**

Se tiver alguma dúvida durante o processo:
1. Verifique se a estrutura de pastas está correta
2. Confirme se todos os arquivos modificados estão na pasta `current/`
3. Teste as modificações antes de fazer commit
4. Use mensagens de commit descritivas

---

**Após organizar tudo, você pode deletar este arquivo de instruções!** 🗑️
