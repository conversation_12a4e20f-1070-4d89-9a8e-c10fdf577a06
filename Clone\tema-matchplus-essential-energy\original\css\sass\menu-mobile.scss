.menu-mobile{
    background: #3d4445;
    height: 100%;
    left: 0;
    // overflow-y: scroll;
    padding: 1em;
    position: fixed;
    top: 0;
    width: 80vw;
    z-index: 20;
    
    // @include prefix('transform', 'translateX(-100vw)');
    left: -100vw;
    @extend .transition;
    
    > h5{
        color: #fff;
        margin-bottom: 10px;
        text-transform: uppercase;
        text-align: center;
        @include typo(1.6rem, 700, $font);
        
        &:after {
            background: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.25);
            border-top: 1px solid rgba(255, 255, 255, 0.25);
            content: '';
            display: block;
            height: 4px;
            margin-top: 10px;
            width: 100%;
        }
    }
    
    .level1{
        position: relative;
        height: 90vh;
        overflow-y: scroll;
    }
    
    .level2{
        display: none;
        margin-top: 1px;
    }
    
    .item-level1{
        border-top: 1px solid rgba(255, 255, 255, 0.25);
        
        &:first-child{
            border-top: none;
        }
    }
    
    .item-level1{
        border-top: 1px solid rgba(255, 255, 255, 0.25);
        margin-top: 1px;
        
        &:first-child{
            border-top: none;
        }
    }
    
    .item-level2{
        margin-top: 1px;
    }
    
    .level1-check{
        display: none;
        
        &:checked ~ .level2{
            display: block;
        }
        
        &:checked ~ .link-level1{
            background: rgba(255,255,255,.3);
            border-bottom: 1px solid transparent;
        }
        
        &:checked ~ .link-level1 .arrow-menu-mobile{
            @include prefix('transform', 'rotate(90deg)');
        }
    }
    
    .link-level1,
    .link-level2,
    .link-level3{
        color: #fff;
        display: block;
        padding: 10px 5px;
        @include typo(1.6rem, 400, $font);
        
        &:hover{
            text-decoration: none;
        }
        
        &:active{
            background: rgba(255,255,255,.3);
        }
    }
    
    .link-level2{
        background: rgba(255,255,255,.3);
        padding-left: 15px;
    }
    
    .link-level3{
        background: rgba(255,255,255,.3);
        padding-left: 30px;
    }
}

.trigger-menu{
    background: #a2a2a2;
    border: none;
    height: 55px;
    margin: 10px 0;
    width: 100%;
    
    @media screen and (max-width: 480px){
        padding: 0;
    }
}

.close-menu{
    position: absolute;
    top: 0;
    left: 100%;
    width: 15vw;
    padding: 15px;
    border-left: 1px solid #000;
    font-size: 14px;
    text-align: center;
    z-index: 99999;
    transition: all 1.5s;
    background: #3d4445;
    color: #fff;
}

html{
    
    .menu-mobile-backdrop{
        background: rgba(0, 0, 0, 0);
        bottom: 0;
        content: '';
        left: 0;
        opacity: 0;
        position: fixed;
        top: 0;
        visibility: hidden;
        width: 100%;
        z-index: 10;
        @extend .transition;
        
    }
    
    .application{
        @include prefix('transform', 'translateX(0)');
        @extend .transition;
    }
    
    &.menu-open{
        overflow: hidden;
        
        .application{
            @include prefix('transform', 'translateX(260px)');
        }
        
        body{
            overflow-x: hidden;
        } 
        
        .menu-mobile{
            // @include prefix('transform', 'translateX(0)');
            left: 0;
        }

        .menu-mobile-backdrop {
            background: rgba(0, 0, 0, 0.85);
            opacity: 1;
            visibility: visible;
        }
        
        .backdrop-icon{
            opacity: 1;
            visibility: visible;
        }
    }
}