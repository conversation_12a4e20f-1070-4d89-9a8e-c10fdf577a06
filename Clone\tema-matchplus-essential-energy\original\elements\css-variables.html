<style>
    html{

    {# Font_ #}
        --font_family                               : '{{ settings.font_default }}', Lato, sans-serif;
        --font_family_title                         : '{{ settings.font_title }}', {{ settings.font_default }}, Lato, sans-serif;
        --font_family_menu                          : '{{ settings.font_menu }}', {{ settings.font_default }}, Lato, sans-serif;
    
    {# Top_Message #}
        --message_color_bg                          : {{ settings.message_color_bg                       }};
        --message_color_txt                        : {{ settings.message_color_txt                      }};
        
    {# General #}
        --color_font_medium                         : {{ settings.color_font_medium                      }};
        --color_font_dark                           : {{ settings.color_font_dark                        }};
        --color_font_inverted                       : {{ settings.color_font_inverted                    }};
        --color_primary                             : {{ settings.color_primary_medium                   }};
        --color_primary_medium                      : {{ settings.color_primary_medium                   }};
        --color_secondary_medium                    : {{ settings.color_secondary_medium                 }};
        --color_gray_medium                         : {{ settings.color_gray_medium                      }};
        --color_gray_dark                           : {{ settings.color_gray_dark                        }};
        --color_font_price                          : {{ settings.color_font_price                       }};
        
        --color_button_buy_text                     : {{ settings.color_button_buy_text                  }};
        --color_button_buy_bg                       : {{ settings.color_button_buy_bg                    }};

    {# Header #}
        --color_header_bg                           : {{ settings.color_header_bg                        }};
        --color_header_text                         : {{ settings.color_header_text                      }};
        --color_header_highlight                    : {{ settings.color_header_highlight                 }};
        --color_header_menu_bg                      : {{ settings.color_header_menu_bg                   }};
        --color_header_menu_text                    : {{ settings.color_header_menu_text                 }};
        --color_header_cart_count                   : {{ settings.color_header_cart_count                }};
        --color_header_cart_count_text              : {{ settings.color_header_cart_count_text           }};
        --color_header_details                      : {{ settings.color_header_details                   }};
        --color_header_search_text                  : {{ settings.color_header_search_text               }};

    {# Footer #}
        --color_newsletter_bg                       : {{ settings.color_newsletter_bg                    }};
        --color_newsletter_text                     : {{ settings.color_newsletter_text                  }};
        --color_newsletter_bg_button                : {{ settings.color_newsletter_bg_button             }};
        --color_newsletter_text_button              : {{ settings.color_newsletter_text_button           }};
        --color_footer_bg                           : {{ settings.color_footer_bg                        }};
        --color_footer_text                         : {{ settings.color_footer_text                      }};
        --color_footer_border                       : {{ settings.color_footer_border                    }};

    {# Selos #}
        --color_seal                                : #086892;
        --color_bg_seal                             : #F1F0FA;
        
    {# Others #}
        {% if settings.height_products %}
            --height_products                       : {{ settings.height_products }}%;
        {% endif %}
        {% if settings.height_products_single %}
            --height_products_single                : {{ settings.height_products_single }}%;
        {% endif %}
        --height_logo_desktop                       : {{ settings.height_desktop_logo }}px;
        --height_logo_mobile                        : {{ settings.height_mobile_logo }}px;
        --height_logo_footer                        : {{ settings.height_footer_logo }}px;
        --countdown_timer_color_txt                 : {{ settings.countdown_timer_color_txt }};
        --countdown_timer_color_bg                  : {{ settings.countdown_timer_color_bg }};
        --showcase_countdown_color_bg               : {{ settings.showcase_countdown_color_bg }};
        --showcase_countdown_color_txt              : {{ settings.showcase_countdown_color_txt }};
        --showcase_countdown_color_bg_button        : {{ settings.showcase_countdown_color_bg_button }};
        --showcase_countdown_color_txt_button       : {{ settings.showcase_countdown_color_txt_button }};
        --showcase_countdown_color_txt_number       : {{ settings.showcase_countdown_color_txt_number }};
        
        --button_whats_color_bg                     : {{ settings.button_whats_color_bg }};
        --button_whats_color_txt                    : {{ settings.button_whats_color_txt }};
        --button_whats_color_border                 : {{ settings.button_whats_color_border }};
        --button_whats_color_hover_bg               : {{ settings.button_whats_color_hover_bg }};
        --button_whats_color_hover_txt              : {{ settings.button_whats_color_hover_txt }};
        --button_whats_color_hover_border           : {{ settings.button_whats_color_hover_border }};
        
        --border_radius_buttons                     : {{ settings.border_radius_buttons }};
        --border_radius_images                      : {{ settings.border_radius_images }};
        
        --video_home_height                         : {{ settings.video_home_height }}%;
        
        
    {# Images and icons #}
        --arrow_select                              : url("data:image/svg+xml,%3Csvg fill='%23{{ settings.text_input|replace({'#': ''}) }}' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 284.929 284.929'%3E%3Cpath d='M282.082,76.511l-14.274-14.273c-1.902-1.906-4.093-2.856-6.57-2.856c-2.471,0-4.661,0.95-6.563,2.856L142.466,174.441 L30.262,62.241c-1.903-1.906-4.093-2.856-6.567-2.856c-2.475,0-4.665,0.95-6.567,2.856L2.856,76.515C0.95,78.417,0,80.607,0,83.082 c0,2.473,0.953,4.663,2.856,6.565l133.043,133.046c1.902,1.903,4.093,2.854,6.567,2.854s4.661-0.951,6.562-2.854L282.082,89.647 c1.902-1.903,2.847-4.093,2.847-6.565C284.929,80.607,283.984,78.417,282.082,76.511z' /%3E%3C/svg%3E%0A");

    }
    {% if settings.height_products_active_zoom %}
        body .product .space-image img,
        body .product-wrapper .product-gallery .product-images img{
            object-fit: cover;
        }
    {% endif %}
    {% if settings.showcase_qty_mobile_active %}
        @media(max-width:768px){
            body .product__buy .product__buy__fields .product__form .label-quantity {
                display: block;
            }
    
            body .product__buy .product__buy__fields.product__buy__fields--opened .product__form {
                flex-wrap: wrap;
            }
    
            body .product__buy .product__buy__fields.product__buy__fields--opened .product__form .label-quantity {
                margin: 0 0 6px 0;
                width: 100%;
            }
    
            body .product__buy .product__buy__fields.product__buy__fields--opened .product__form .product__quantity {
                height: 33px;
            }
        }
    {% endif %}
</style>
